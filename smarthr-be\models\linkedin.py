from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, field_validator
from enum import Enum


class LinkedInSearchType(str, Enum):
    """Types of LinkedIn searches supported."""
    PEOPLE = "people"


class LinkedInExperienceLevel(str, Enum):
    """LinkedIn experience levels for filtering."""
    INTERNSHIP = "1"
    ENTRY_LEVEL = "2"
    ASSOCIATE = "3"
    MID_SENIOR = "4"
    DIRECTOR = "5"
    EXECUTIVE = "6"


class LinkedInLocation(BaseModel):
    """LinkedIn location information."""
    name: str
    country_code: Optional[str] = None
    region: Optional[str] = None
    city: Optional[str] = None
    geo_id: Optional[str] = None


class LinkedInCompany(BaseModel):
    """LinkedIn company information."""
    id: Optional[str] = None
    name: str
    industry: Optional[str] = None
    size: Optional[str] = None
    logo_url: Optional[str] = None
    website: Optional[str] = None


class LinkedInExperience(BaseModel):
    """LinkedIn work experience entry."""
    title: str
    company: LinkedInCompany
    location: Optional[LinkedInLocation] = None
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    duration: Optional[str] = None
    description: Optional[str] = None
    is_current: bool = False


class LinkedInEducation(BaseModel):
    """LinkedIn education entry."""
    school: str
    degree: Optional[str] = None
    field_of_study: Optional[str] = None
    start_year: Optional[int] = None
    end_year: Optional[int] = None
    description: Optional[str] = None


class LinkedInSkill(BaseModel):
    """LinkedIn skill information."""
    name: str
    endorsements: Optional[int] = 0
    is_top_skill: bool = False


class LinkedInProfile(BaseModel):
    """Complete LinkedIn profile data structure."""
    id: Optional[str] = None
    public_id: Optional[str] = None
    first_name: str
    last_name: str
    full_name: Optional[str] = None
    headline: Optional[str] = None
    summary: Optional[str] = None
    location: Optional[LinkedInLocation] = None
    profile_url: Optional[str] = None
    profile_picture_url: Optional[str] = None
    connections_count: Optional[int] = None
    followers_count: Optional[int] = None
    experience: List[LinkedInExperience] = []
    education: List[LinkedInEducation] = []
    skills: List[LinkedInSkill] = []
    languages: List[str] = []
    certifications: List[str] = []
    volunteer_experience: List[Dict[str, Any]] = []
    recommendations_count: Optional[int] = None
    last_updated: Optional[datetime] = None

    @field_validator('full_name', mode='before')
    @classmethod
    def set_full_name(cls, v, info):
        if not v and info.data:
            first_name = info.data.get('first_name')
            last_name = info.data.get('last_name')
            if first_name and last_name:
                return f"{first_name} {last_name}"
        return v


class LinkedInSearchFilters(BaseModel):
    """Dynamic search filters for LinkedIn people searches."""
    keywords: Optional[List[str]] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    full_name: Optional[str] = None
    location: Optional[str] = None
    country: Optional[str] = None
    city: Optional[str] = None
    experience_level: Optional[LinkedInExperienceLevel] = None
    skills: Optional[List[str]] = None
    school: Optional[str] = None
    degree: Optional[str] = None
    years_of_experience: Optional[int] = None
    languages: Optional[List[str]] = None

    # Advanced filters
    connections_of: Optional[str] = None
    followers_count_min: Optional[int] = None
    followers_count_max: Optional[int] = None
    profile_language: Optional[str] = None

    # Search behavior
    limit: int = Field(default=25, ge=1, le=100)
    offset: int = Field(default=0, ge=0)
    sort_by: Optional[str] = Field(default="relevance", pattern="^(relevance|recent|connections)$")


class LinkedInSearchRequest(BaseModel):
    """Request model for LinkedIn search operations."""
    search_type: LinkedInSearchType = LinkedInSearchType.PEOPLE
    filters: LinkedInSearchFilters
    project_id: Optional[str] = None
    requested_by: str = Field(default="api_user", description="User or system requesting the search")
    search_id: Optional[str] = None
    
    class Config:
        use_enum_values = True


class LinkedInSearchResponse(BaseModel):
    """Response model for LinkedIn search results."""
    search_id: str
    total_results: int
    returned_results: int
    profiles: List[LinkedInProfile]
    search_filters_used: LinkedInSearchFilters
    search_metadata: Dict[str, Any] = {}
    execution_time_ms: Optional[int] = None
    rate_limit_remaining: Optional[int] = None
    next_page_token: Optional[str] = None
    success: bool = True
    error_message: Optional[str] = None
    
    class Config:
        use_enum_values = True


# Agent Communication Models
class LinkedInAgent1Request(BaseModel):
    """Request model for LinkedIn Search Agent (Agent 1)."""
    search_request: LinkedInSearchRequest
    agent_id: str = "linkedin_search_agent"
    timestamp: datetime = Field(default_factory=datetime.now)


class LinkedInAgent1Response(BaseModel):
    """Response model from LinkedIn Search Agent (Agent 1)."""
    search_results: LinkedInSearchResponse
    success: bool
    error_message: Optional[str] = None
    agent_id: str = "linkedin_search_agent"
    processing_time_ms: Optional[int] = None
    timestamp: datetime = Field(default_factory=datetime.now)


class SchemaTransformationRequest(BaseModel):
    """Request model for Schema Transformation Agent (Agent 2)."""
    linkedin_profiles: List[LinkedInProfile]
    target_schema: str = "smarthr_candidate"
    transformation_rules: Optional[Dict[str, Any]] = None
    agent_id: str = "schema_transformation_agent"
    timestamp: datetime = Field(default_factory=datetime.now)


class TransformedCandidate(BaseModel):
    """Transformed candidate data in smartHR format."""
    candidate_info: Dict[str, Any]
    source_linkedin_profile: LinkedInProfile
    transformation_confidence: float = Field(ge=0.0, le=1.0)
    transformation_notes: Optional[str] = None
    missing_fields: List[str] = []
    additional_fields: List[str] = []


class SchemaTransformationResponse(BaseModel):
    """Response model from Schema Transformation Agent (Agent 2)."""
    transformed_candidates: List[TransformedCandidate]
    success: bool
    error_message: Optional[str] = None
    transformation_summary: Optional[str] = None
    agent_id: str = "schema_transformation_agent"
    processing_time_ms: Optional[int] = None
    timestamp: datetime = Field(default_factory=datetime.now)


class LinkedInOrchestrationRequest(BaseModel):
    """Main orchestration request for the complete LinkedIn workflow."""
    search_request: LinkedInSearchRequest
    transform_to_smarthr: bool = True
    orchestration_id: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.now)


class LinkedInOrchestrationResponse(BaseModel):
    """Complete orchestration response with both search and transformation results."""
    orchestration_id: str
    search_results: LinkedInSearchResponse
    transformed_candidates: Optional[List[TransformedCandidate]] = None
    success: bool
    error_message: Optional[str] = None
    agent_execution_log: List[Dict[str, Any]] = []
    total_processing_time_ms: Optional[int] = None
    timestamp: datetime = Field(default_factory=datetime.now)


# Error Models
class LinkedInAPIError(Exception):
    """LinkedIn API error exception."""

    def __init__(
        self,
        error_code: str,
        error_message: str,
        error_details: Optional[Dict[str, Any]] = None,
        retry_after: Optional[int] = None,
        rate_limit_exceeded: bool = False
    ):
        self.error_code = error_code
        self.error_message = error_message
        self.error_details = error_details or {}
        self.retry_after = retry_after
        self.rate_limit_exceeded = rate_limit_exceeded
        super().__init__(error_message)

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "error_code": self.error_code,
            "error_message": self.error_message,
            "error_details": self.error_details,
            "retry_after": self.retry_after,
            "rate_limit_exceeded": self.rate_limit_exceeded
        }


class AgentError(BaseModel):
    """Agent-specific error information."""
    agent_id: str
    error_type: str
    error_message: str
    error_details: Optional[Dict[str, Any]] = None
    recoverable: bool = True
    suggested_action: Optional[str] = None
