# LinkedIn JSON Schema Analysis

## Overview

This document analyzes LinkedIn's JSON response structures to understand data patterns for effective schema transformation to smartHR candidate format.

## LinkedIn API Response Formats

### 1. Official LinkedIn API (v2) Response Structure

#### People Search Response
```json
{
  "elements": [
    {
      "id": "urn:li:person:ABC123",
      "firstName": {
        "localized": {
          "en_US": "John"
        }
      },
      "lastName": {
        "localized": {
          "en_US": "Doe"
        }
      },
      "headline": {
        "localized": {
          "en_US": "Software Engineer at Tech Company"
        }
      },
      "location": {
        "name": "San Francisco, CA",
        "countryCode": "US",
        "region": "California"
      },
      "industry": "Computer Software",
      "positions": {
        "values": [
          {
            "id": *********,
            "title": "Senior Software Engineer",
            "company": {
              "id": *********,
              "name": "Tech Company Inc",
              "industry": "Computer Software",
              "size": "1001-5000 employees"
            },
            "location": {
              "name": "San Francisco, CA"
            },
            "startDate": {
              "year": 2020,
              "month": 3
            },
            "endDate": null,
            "isCurrent": true,
            "description": "Lead development of web applications..."
          }
        ]
      },
      "educations": {
        "values": [
          {
            "id": *********,
            "school": {
              "name": "Stanford University"
            },
            "degree": "Bachelor of Science",
            "fieldOfStudy": "Computer Science",
            "startDate": {
              "year": 2016
            },
            "endDate": {
              "year": 2020
            }
          }
        ]
      },
      "skills": {
        "values": [
          {
            "name": "JavaScript",
            "endorsements": 45
          },
          {
            "name": "Python",
            "endorsements": 32
          }
        ]
      }
    }
  ],
  "paging": {
    "count": 25,
    "start": 0,
    "total": 1247
  }
}
```

### 2. LinkedIn Web/Voyager API Response Structure

#### Search Results Response
```json
{
  "included": [
    {
      "$type": "com.linkedin.voyager.search.SearchProfile",
      "hitInfo": {
        "firstName": "John",
        "lastName": "Doe",
        "headline": "Software Engineer at Tech Company",
        "publicIdentifier": "johndoe123",
        "location": {
          "displayName": "San Francisco Bay Area"
        },
        "industry": "Computer Software",
        "connectionDegree": "DISTANCE_2"
      },
      "entityUrn": "urn:li:fs_profile:ACoAABCDEFG",
      "profilePicture": {
        "displayImageUrn": "urn:li:digitalmediaAsset:C4D05AQHxyz123"
      }
    }
  ],
  "paging": {
    "count": 10,
    "start": 0,
    "total": 500
  }
}
```

### 3. Third-Party API Response Structure

#### Typical Third-Party Response
```json
{
  "profiles": [
    {
      "id": "linkedin_123456",
      "first_name": "John",
      "last_name": "Doe",
      "full_name": "John Doe",
      "headline": "Software Engineer at Tech Company",
      "summary": "Experienced software engineer with 5+ years...",
      "location": "San Francisco, CA, United States",
      "profile_url": "https://www.linkedin.com/in/johndoe123",
      "profile_picture": "https://media.licdn.com/dms/image/...",
      "connections": 500,
      "followers": 1200,
      "experience": [
        {
          "title": "Senior Software Engineer",
          "company": "Tech Company Inc",
          "company_url": "https://www.linkedin.com/company/tech-company",
          "location": "San Francisco, CA",
          "start_date": "2020-03",
          "end_date": null,
          "duration": "3 years 9 months",
          "description": "Lead development of web applications..."
        }
      ],
      "education": [
        {
          "school": "Stanford University",
          "degree": "Bachelor of Science",
          "field": "Computer Science",
          "start_year": 2016,
          "end_year": 2020
        }
      ],
      "skills": [
        "JavaScript",
        "Python",
        "React",
        "Node.js"
      ]
    }
  ],
  "total_results": 1247,
  "page": 1,
  "per_page": 25
}
```

## SmartHR Candidate Schema Target

### Target SmartHR Candidate Structure
```json
{
  "candidate_info": {
    "first_name": "John",
    "last_name": "Doe",
    "email": "<EMAIL>",
    "phone": "******-123-4567",
    "location": "San Francisco, CA",
    "current_position": "Senior Software Engineer",
    "professional_summary": "Experienced software engineer with 5+ years in web development...",
    "experience": [
      {
        "title": "Senior Software Engineer",
        "company": "Tech Company Inc",
        "location": "San Francisco, CA",
        "start_date": "2020-03",
        "end_date": null,
        "is_current": true,
        "description": "Lead development of web applications..."
      }
    ],
    "education": [
      {
        "school": "Stanford University",
        "degree": "Bachelor of Science",
        "field_of_study": "Computer Science",
        "start_year": 2016,
        "end_year": 2020
      }
    ],
    "skills": [
      "JavaScript",
      "Python",
      "React",
      "Node.js"
    ],
    "languages": ["English", "Spanish"],
    "certifications": [],
    "linkedin_url": "https://www.linkedin.com/in/johndoe123"
  }
}
```

## Schema Transformation Mapping

### Field Mapping Rules

| LinkedIn Field | SmartHR Field | Transformation Notes |
|----------------|---------------|---------------------|
| `firstName.localized.en_US` or `first_name` | `first_name` | Direct mapping, normalize case |
| `lastName.localized.en_US` or `last_name` | `last_name` | Direct mapping, normalize case |
| `headline.localized.en_US` or `headline` | `current_position` | Extract job title from headline |
| `summary` or `description` | `professional_summary` | Direct mapping or generate from headline |
| `location.name` or `location` | `location` | Parse and normalize location string |
| `positions.values[]` or `experience[]` | `experience[]` | Transform experience array |
| `educations.values[]` or `education[]` | `education[]` | Transform education array |
| `skills.values[].name` or `skills[]` | `skills[]` | Extract skill names |
| `publicIdentifier` or `profile_url` | `linkedin_url` | Generate full LinkedIn URL |

### Data Quality Patterns

#### Common Data Issues
1. **Missing Contact Information**: LinkedIn rarely provides email/phone in API responses
2. **Inconsistent Date Formats**: Various formats (YYYY-MM, YYYY, "Present")
3. **Nested Localization**: Official API uses nested localized objects
4. **Company Information**: May be incomplete or inconsistent
5. **Location Variations**: Different formats ("City, State", "City, Country", etc.)

#### Data Enrichment Opportunities
1. **Professional Summary Generation**: Create from headline + experience
2. **Skill Prioritization**: Use endorsement counts for ranking
3. **Experience Duration**: Calculate from start/end dates
4. **Location Standardization**: Normalize to consistent format
5. **Company Normalization**: Clean company names and industries

## Transformation Strategies

### 1. Direct Field Mapping
- Simple 1:1 field mappings where data structure is similar
- Apply basic normalization (trim, case conversion)

### 2. Computed Field Generation
- Generate `professional_summary` from `headline` and `summary`
- Calculate experience duration from date ranges
- Create full LinkedIn URL from public identifier

### 3. Array Transformation
- Transform nested experience/education arrays
- Flatten and normalize skill arrays
- Handle missing or null array elements

### 4. Data Enrichment
- Infer missing fields from available context
- Standardize location and company information
- Prioritize skills by endorsement count

### 5. Fallback Strategies
- Use "not_provided" for missing required fields
- Generate minimal profiles when transformation fails
- Maintain original LinkedIn data for reference

## Confidence Scoring

### Factors Affecting Transformation Confidence

1. **Data Completeness** (40% weight)
   - Presence of required fields
   - Quality of available data

2. **Data Quality** (30% weight)
   - Consistency of information
   - Absence of parsing errors

3. **Transformation Success** (20% weight)
   - Successful field mappings
   - No data loss during transformation

4. **Enrichment Success** (10% weight)
   - Successful data enrichment
   - Generated field quality

### Confidence Score Calculation
```
confidence = (
  (required_fields_present / total_required_fields) * 0.4 +
  (quality_score) * 0.3 +
  (successful_mappings / total_mappings) * 0.2 +
  (enrichment_score) * 0.1
)
```

## Implementation Considerations

### Performance Optimization
- Batch process multiple profiles
- Cache common transformations
- Parallel processing for large datasets

### Error Handling
- Graceful degradation for missing fields
- Fallback to minimal transformation
- Comprehensive logging for debugging

### Validation
- Schema validation for transformed data
- Business rule validation
- Data quality checks

### Monitoring
- Track transformation success rates
- Monitor confidence score distributions
- Alert on unusual patterns or failures
