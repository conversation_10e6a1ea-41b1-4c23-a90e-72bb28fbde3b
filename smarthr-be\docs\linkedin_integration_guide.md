# LinkedIn People Search Integration Guide

## Overview

The LinkedIn People Search Integration system provides a comprehensive solution for searching LinkedIn people/candidates and transforming their profiles into the smartHR candidate format. The system focuses exclusively on people search (not companies or jobs) and uses a 2-agent architecture similar to the existing 4-agent interview evaluation system.

## Architecture

### LinkedIn API Integration
The system uses official LinkedIn API methods as specified in LinkedIn's API documentation:

- **FINDER Method**: `GET https://api.linkedin.com/v2/people?q=search` - For searching people
- **GET Method**: `GET https://api.linkedin.com/v2/people/{profileId}` - For retrieving single profiles
- **BATCH_GET Method**: `GET https://api.linkedin.com/v2/people?ids=List(id1,id2,id3)` - For retrieving multiple profiles

### Agent System
1. **LinkedIn People Search Agent** - Handles LinkedIn API integration using official API methods
2. **Schema Transformation Agent** - Transforms LinkedIn people profiles to smartHR candidate format using LLM-powered enhancement

### Key Components
- **Workflow Orchestrator** - Coordinates the complete pipeline
- **Agent Communication Protocol** - Manages data flow between agents
- **Response Processing** - Normalizes and enriches LinkedIn data
- **Schema Mapping** - Handles field transformations
- **Validation System** - Ensures data quality and completeness
- **Error Recovery** - Provides robust error handling and recovery

## Quick Start

### 1. Configuration

Create environment variables for LinkedIn integration:

```bash
# LinkedIn API Configuration
LINKEDIN_API_PROVIDER=mock  # Options: official, web_scraping, third_party, mock
LINKEDIN_API_KEY=your_api_key_here
LINKEDIN_API_SECRET=your_api_secret_here

# Rate Limiting
LINKEDIN_RATE_LIMIT_PER_MINUTE=60
LINKEDIN_API_TIMEOUT_SECONDS=30

# LLM Configuration for Transformation
LINKEDIN_LLM_MODELS=groq/llama3-70b-8192,azure/gpt-4
LINKEDIN_USE_FALLBACK_TRANSFORMATION=true
LINKEDIN_CONFIDENCE_THRESHOLD=0.6
```

### 2. Basic Usage

```python
import asyncio
from controllers.linkedin_workflow_orchestrator import execute_linkedin_candidate_search
from models.linkedin_config import load_linkedin_config

async def search_candidates():
    # Load configuration
    config = load_linkedin_config()
    
    # Execute search
    result = await execute_linkedin_candidate_search(
        config=config,
        keywords=["Python developer", "Software engineer"],
        location="San Francisco, CA",
        skills=["Python", "Django"],
        limit=25
    )
    
    if result["success"]:
        print(f"Found {result['workflow_summary']['profiles_found']} profiles")
        print(f"Transformed {result['workflow_summary']['candidates_transformed']} candidates")
        
        # Access transformed candidates
        candidates = result["transformation_results"]["transformed_candidates"]
        for candidate in candidates:
            print(f"Candidate: {candidate.candidate_info['first_name']} {candidate.candidate_info['last_name']}")
    else:
        print(f"Search failed: {result['error_message']}")

# Run the search
asyncio.run(search_candidates())
```

### 3. API Endpoints

The system provides REST API endpoints:

```bash
# Health check
GET /api/external_source/health

# Get system status
GET /api/external_source/status

# Search people/candidates (FINDER method)
POST /api/external_source/search
{
    "keywords": ["Python developer", "Software engineer"],
    "location": "San Francisco, CA",
    "skills": ["Python", "Django", "FastAPI"],
    "school": "Stanford University",
    "limit": 25,
    "transform_profiles": true
}

# Get single profile by ID (GET method)
GET /api/external_source/profile/{profileId}

# Get multiple profiles by IDs (BATCH_GET method)
POST /api/external_source/profiles/batch
[
    "profile_id_1",
    "profile_id_2",
    "profile_id_3"
]

# Batch people search
POST /api/external_source/search/batch
[
    {
        "keywords": ["Python developer"],
        "location": "San Francisco, CA",
        "skills": ["Python"],
        "limit": 10
    },
    {
        "keywords": ["Frontend engineer"],
        "location": "New York, NY",
        "skills": ["React", "JavaScript"],
        "limit": 15
    }
]

# Test integration
POST /api/external_source/test
{
    "keywords": ["Software Engineer"],
    "limit": 5
}
```

## Configuration Options

### LinkedIn API Methods Used

The integration implements LinkedIn's official API methods:

1. **FINDER Method** - `GET /v2/people?q=search`
   - Primary method for people search
   - Supports keywords, location, company, title filters
   - Returns paginated results with count/start parameters

2. **GET Method** - `GET /v2/people/{profileId}`
   - Retrieves single profile by ID
   - Used for detailed profile information
   - Requires specific profile identifier

3. **BATCH_GET Method** - `GET /v2/people?ids=List(id1,id2,id3)`
   - Retrieves multiple profiles in single request
   - More efficient than multiple GET requests
   - Supports up to 50 IDs per request

### API Providers

1. **Official LinkedIn API** (Recommended)
   - Uses proper LinkedIn API methods
   - Requires LinkedIn API credentials and partnership
   - Most reliable and compliant approach
   - Best data quality and rate limits

2. **Mock Provider** (Default for testing)
   - Simulates LinkedIn API responses
   - No API keys required
   - Good for development and testing
   - Implements same API method patterns

3. **Web Scraping** (Fallback)
   - Uses LinkedIn web interface
   - Requires careful rate limiting
   - May have legal considerations
   - Not recommended for production

4. **Third-Party APIs** (Alternative)
   - Uses services like RapidAPI
   - Requires third-party API keys
   - Variable quality and cost
   - May not follow official API patterns

### Transformation Options

- **LLM Enhancement**: Uses language models to improve data quality
- **Fallback Transformation**: Rule-based transformation when LLM fails
- **Validation**: Comprehensive data validation and quality checks
- **Confidence Scoring**: Provides confidence scores for transformations

## Data Flow

```
1. Search Request → LinkedIn Search Agent
2. LinkedIn API Call → Raw Profile Data
3. Response Processing → Normalized LinkedIn Profiles
4. Schema Transformation Agent → smartHR Candidate Format
5. Validation & Quality Assessment → Final Results
```

## SmartHR Candidate Schema

The system transforms LinkedIn profiles to this smartHR format:

```json
{
    "first_name": "John",
    "last_name": "Doe",
    "email": "not_provided",
    "phone": "not_provided",
    "location": "San Francisco, CA",
    "current_position": "Senior Software Engineer",
    "professional_summary": "Experienced software engineer with 5+ years...",
    "experience": [
        {
            "title": "Senior Software Engineer",
            "company": "Tech Company Inc",
            "location": "San Francisco, CA",
            "start_date": "2020-03",
            "end_date": null,
            "is_current": true,
            "description": "Lead development of web applications..."
        }
    ],
    "education": [
        {
            "school": "Stanford University",
            "degree": "Bachelor of Science",
            "field_of_study": "Computer Science",
            "start_year": 2016,
            "end_year": 2020
        }
    ],
    "skills": ["JavaScript", "Python", "React", "Node.js"],
    "linkedin_url": "https://www.linkedin.com/in/johndoe123",
    "source": "linkedin",
    "imported_at": "2024-01-15T10:30:00Z"
}
```

## Error Handling

The system includes comprehensive error handling:

### Error Types
- **Rate Limiting**: Automatic retry with backoff
- **Authentication**: Clear error messages and guidance
- **Network Issues**: Retry with exponential backoff
- **API Errors**: Fallback to alternative providers
- **Transformation Errors**: Fallback to rule-based transformation

### Recovery Strategies
- **Retry**: For transient errors
- **Fallback**: Use alternative methods
- **Skip**: Continue with other profiles
- **Abort**: For critical errors

## Quality Assessment

Each search includes quality assessment:

- **Search Quality**: Profile count, completeness, relevance
- **Transformation Quality**: Confidence scores, validation results
- **Overall Score**: Combined quality metric
- **Recommendations**: Actionable improvement suggestions

## Monitoring and Logging

### Logging Levels
- **INFO**: Normal operations and results
- **WARNING**: Non-critical issues
- **ERROR**: Failed operations
- **DEBUG**: Detailed debugging information

### Metrics Tracked
- Search success rates
- Transformation confidence scores
- Processing times
- Error frequencies
- Quality scores

## Examples

See `examples/linkedin_integration_example.py` for comprehensive usage examples:

- Basic candidate search
- Advanced multi-scenario searches
- API testing and validation
- Error handling demonstrations

## Testing

### Unit Tests
```bash
# Run LinkedIn integration tests
pytest tests/test_linkedin_integration.py -v
```

### Integration Tests
```bash
# Run full workflow tests
pytest tests/test_linkedin_workflow.py -v
```

### Manual Testing
```bash
# Run example script
python examples/linkedin_integration_example.py
```

## Troubleshooting

### Common Issues

1. **Configuration Not Found**
   - Check environment variables
   - Verify configuration file exists
   - Ensure proper permissions

2. **API Rate Limiting**
   - Reduce request frequency
   - Check rate limit settings
   - Consider using different API provider

3. **Low Quality Scores**
   - Review search keywords
   - Adjust search filters
   - Check data completeness

4. **Transformation Failures**
   - Verify LLM configuration
   - Check fallback settings
   - Review error logs

### Debug Mode

Enable debug logging:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

### Health Checks

Monitor system health:
```bash
curl http://localhost:8080/api/external_source/health
curl http://localhost:8080/api/external_source/status
```

## Performance Optimization

### Batch Processing
- Use batch endpoints for multiple searches
- Configure appropriate concurrency limits
- Monitor resource usage

### Caching
- Enable profile caching for repeated searches
- Configure cache duration appropriately
- Monitor cache hit rates

### Rate Limiting
- Configure appropriate rate limits
- Use exponential backoff
- Monitor API usage

## Security Considerations

- Store API credentials securely
- Use environment variables for sensitive data
- Implement proper authentication
- Monitor for unusual usage patterns
- Respect LinkedIn's terms of service

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review error logs
3. Test with mock provider
4. Check configuration settings
5. Consult the examples

## Future Enhancements

Planned improvements:
- Additional API providers
- Enhanced data enrichment
- Real-time candidate monitoring
- Advanced search filters
- Integration with ATS systems
