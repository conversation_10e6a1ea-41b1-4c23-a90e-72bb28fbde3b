# match_service.py
from contextlib import contextmanager
from datetime import datetime
import logging
import os
from fastapi import HTTPException
import psycopg2
from psycopg2.extras import <PERSON><PERSON>, RealDictCursor
from core.config import Settings
from models.matching_result import MatchingResult, MatchingResults, CosineSimilarity, MatchingResultCreate
from utils import match_evaluations as match_functions

# Telemetry Section
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

threshold_match = int(os.getenv("MAXIMUM_NUMBER_OF_MATCHES", "50"))


def _handle_matching_result_nulls(row_dict):
    """Helper function to handle NULL values in MatchingResult creation"""
    if row_dict['analysis_data'] is None:
        row_dict['analysis_data'] = {}
    if row_dict['last_position_update'] is None:
        row_dict['last_position_update'] = row_dict['created_at']
    if row_dict['last_candidate_update'] is None:
        row_dict['last_candidate_update'] = row_dict['created_at']
    if row_dict['info'] is None:
        row_dict['info'] = {}
    return row_dict

# DB helper to get cursor


@contextmanager
def get_cursor():
    """
    Context manager for PostgreSQL cursor with error handling.
    """
    conn = None
    try:
        conn = psycopg2.connect(
            Settings.DATABASE_URL,
            connect_timeout=120,
            options="-c statement_timeout=120000",
            keepalives_idle=30
        )
        with conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                yield cur
    except psycopg2.Error as e:
        logger.error(f"Database error occurred in get_cursor: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
    finally:
        if conn:
            conn.close()


# Insert new record or update existing record on database table
def upsert_matching_result(data: MatchingResultCreate) -> dict:
    with get_cursor() as cur:
        cur.execute(
            """
            INSERT INTO smart_matching_results (position_id, candidate_id, cosine_similarity, analysis_data, last_position_update, last_candidate_update, created_by, updated_by)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            ON CONFLICT (position_id, candidate_id) DO UPDATE
            SET
                cosine_similarity = EXCLUDED.cosine_similarity,
                analysis_data = EXCLUDED.analysis_data,
                last_position_update = EXCLUDED.last_position_update,
                last_candidate_update = EXCLUDED.last_candidate_update,
                updated_by = EXCLUDED.updated_by,
                updated_at = NOW()
            RETURNING id, position_id, candidate_id, cosine_similarity, analysis_data, last_position_update, last_candidate_update, created_at, created_by, updated_at, updated_by
            """,
            (data.position_id, data.candidate_id, data.cosine_similarity, Json(data.analysis_data), data.last_position_update, data.last_candidate_update, data.created_by, data.updated_by)
        )
        row = cur.fetchone()
        return dict(row)


# Insert new record on database table
def insert_cosine_similarity(data: CosineSimilarity) -> dict:
    with get_cursor() as cur:
        cur.execute(
            """
            INSERT INTO smart_matching_results (position_id, candidate_id, cosine_similarity, created_by, updated_by)
            VALUES (%s, %s, %s, %s, %s)
            ON CONFLICT (position_id, candidate_id) DO UPDATE
            SET
                cosine_similarity = EXCLUDED.cosine_similarity,
                updated_by = EXCLUDED.updated_by,
                updated_at = NOW()
            RETURNING id, position_id, candidate_id, cosine_similarity, analysis_data, last_position_update, last_candidate_update, created_at, created_by, updated_at, updated_by
            """,
            (data.position_id, data.candidate_id, data.cosine_similarity, data.created_by, data.updated_by)
        )
        row = cur.fetchone()
        return dict(row)


# Get by position_id and candidate_id
def get_matching_result(position_id: str, candidate_id: str) -> dict:
    with get_cursor() as cur:
        cur.execute(
            """
            SELECT id, position_id, candidate_id, cosine_similarity, analysis_data, last_position_update, last_candidate_update, created_at, created_by, updated_at, updated_by FROM smart_matching_results
            WHERE position_id = %s AND candidate_id = %s
            """,
            (position_id, candidate_id)
        )
        row = cur.fetchone()
        return dict(row)


# Get by position_id
def matching_results_by_position(position_id: str, created_by: str, updated_by: str) -> MatchingResults:
    with get_cursor() as cur:
        query = """
            SELECT smr.id, smr.position_id, smr.candidate_id, smr.cosine_similarity, smr.analysis_data, smr.last_position_update, smr.last_candidate_update, smr.created_at, smr.created_by, smr.updated_at, smr.updated_by, c.candidate_info as info
            FROM smart_matching_results as smr
            JOIN candidates_smarthr as c ON c.id = smr.candidate_id
            WHERE position_id = %s
            ORDER BY (analysis_data->>'compatibilityPercentage')::float DESC
            """

        def execute_query():
            cur.execute(
                query,
                (position_id,)
            )
            return cur.fetchall()
        rows = execute_query()
        if not rows:
            generate_match_for_position(position_id, created_by, updated_by)
            rows = execute_query()

        if check_and_generate_smart_match_for_position(position_id, rows[0]['last_position_update'], created_by):
            rows = execute_query()

        for row in rows[:threshold_match]:
            if row['cosine_similarity'] is None or row['analysis_data'] is None:
                if row['cosine_similarity'] is None:
                    generate_smart_match_and_cosine_similarity(position_id, row['candidate_id'], created_by, updated_by)
                elif row[4] is None:
                    generate_smart_match(position_id, row['candidate_id'], created_by, updated_by, row['cosine_similarity'])
        # Re-fetch rows if any were updated
        rows = execute_query()
        return MatchingResults(results=[MatchingResult(**_handle_matching_result_nulls(dict(row))) for row in rows], total_items=len(rows))


# Get by candidate_id
def matching_results_by_candidate(candidate_id: str, created_by: str, updated_by: str) -> MatchingResults:
    with get_cursor() as cur:
        query = """
            SELECT smr.id, smr.position_id, smr.candidate_id, smr.cosine_similarity, smr.analysis_data, smr.last_position_update, smr.last_candidate_update, smr.created_at, smr.created_by, smr.updated_at, smr.updated_by, p.position_info as info
            FROM smart_matching_results as smr
            JOIN positions_smarthr as p ON p.id = smr.position_id
            WHERE candidate_id = %s
            ORDER BY (analysis_data->>'compatibilityPercentage')::float DESC
            """

        def execute_query():
            cur.execute(
                query,
                (candidate_id,)
            )
            return cur.fetchall()
        rows = execute_query()

        if not rows:
            generate_match_for_candidate(candidate_id, created_by, updated_by)
            rows = execute_query()

        if check_and_generate_smart_match_for_candidate(candidate_id, rows[0]['last_candidate_update'], created_by):
            rows = execute_query()

        for row in rows[:threshold_match]:
            if row['cosine_similarity'] is None or row['analysis_data'] is None:
                if row['cosine_similarity'] is None:
                    generate_smart_match_and_cosine_similarity(row['position_id'], candidate_id, created_by, updated_by)
                elif row['analysis_data'] is None:
                    generate_smart_match(row['position_id'], candidate_id, created_by, updated_by, row['cosine_similarity'])
        # Re-fetch rows if any were updated
        rows = execute_query()
        # return MatchingResults object
        return MatchingResults(results=[MatchingResult(**_handle_matching_result_nulls(dict(row))) for row in rows], total_items=len(rows))


# Generate match when position is updated or created
def generate_match_for_position(position_id: str, created_by: str, updated_by: str) -> None:
    with get_cursor() as cur:
        # Get position embedding
        cur.execute(
            "SELECT embedding, to_be_embebbed, updated_at FROM positions_smarthr WHERE id=%s",
            (position_id,),
        )
        position_result = cur.fetchone()
        if not position_result:
            return
        position_embedding = position_result['embedding']
        position_txt = position_result['to_be_embebbed']
        position_updated_at = position_result['updated_at']
        if not position_embedding:
            return
        # Compare position_embedding with candidates embeddings
        cur.execute(
            """SELECT
                id,
                1 - (embedding <=> %s::vector) AS cosine_similarity,
                to_be_embebbed,
                updated_at
            FROM
                candidates_smarthr
            WHERE is_deleted = false and is_active = true
            AND embedding IS NOT NULL
            ORDER BY cosine_similarity DESC
            """,
            (position_embedding,),
        )
        results = cur.fetchall()
        # for the firsts 50 results, generate match and insert matching result
        for candidate in results[:threshold_match]:
            candidate_id = candidate['id']
            cosine_similarity = candidate['cosine_similarity']
            candidate_txt = candidate['to_be_embebbed']
            candidate_updated_at = candidate['updated_at']
            # Generate match
            analysis_data = match_functions.get_candidate_analysis_custom_prompt(
                candidate_txt, position_txt
            )
            # Convert CompatibilityEvaluation to dict
            analysis_data_dict = analysis_data.model_dump() if hasattr(analysis_data, 'model_dump') else analysis_data
            # Insert match
            upsert_matching_result(MatchingResultCreate(
                position_id=position_id,
                candidate_id=candidate_id,
                cosine_similarity=cosine_similarity,
                analysis_data=analysis_data_dict,
                last_position_update=position_updated_at,
                last_candidate_update=candidate_updated_at,
                created_by=created_by,
                updated_by=updated_by
            ))
        # for the rests, insert cosine similarity
        for candidate in results[threshold_match:]:
            candidate_id = candidate['id']
            cosine_similarity = candidate['cosine_similarity']
            candidate_updated_at = candidate['updated_at']
            insert_cosine_similarity(CosineSimilarity(
                position_id=position_id,
                candidate_id=candidate_id,
                cosine_similarity=cosine_similarity,
                created_by=created_by,
                updated_by=updated_by
            ))


# Generate match when candidate is updated or created
def generate_match_for_candidate(candidate_id: str, created_by: str, updated_by: str) -> None:
    with get_cursor() as cur:
        # Get candidate embedding
        cur.execute(
            "SELECT embedding, to_be_embebbed, updated_at FROM candidates_smarthr WHERE id=%s",
            (candidate_id,),
        )
        candidate_result = cur.fetchone()
        if not candidate_result:
            return
        candidate_embedding = candidate_result['embedding']
        candidate_txt = candidate_result['to_be_embebbed']
        candidate_updated_at = candidate_result['updated_at']
        if not candidate_embedding:
            return
        # Compare candidate_embedding with positions embeddings
        cur.execute(
            """SELECT
                id,
                1 - (embedding <=> %s::vector) AS cosine_similarity,
                to_be_embebbed,
                updated_at
            FROM
                positions_smarthr
            WHERE (position_info -> 'reasonStatus' IS NULL OR position_info -> 'reasonStatus' ->> 'reason' IS NULL OR TRIM(position_info -> 'reasonStatus' ->> 'reason') = '')
            AND embedding IS NOT NULL
            ORDER BY cosine_similarity DESC
            """,
            (candidate_embedding,),
        )
        results = cur.fetchall()
        # for the firsts 50 results, generate match and insert matching result
        for position in results[:threshold_match]:
            position_id = position['id']
            cosine_similarity = position['cosine_similarity']
            position_txt = position['to_be_embebbed']
            position_updated_at = position['updated_at']
            # Generate match
            analysis_data = match_functions.get_candidate_analysis_custom_prompt(
                candidate_txt, position_txt
            )
            # Convert CompatibilityEvaluation to dict
            analysis_data_dict = analysis_data.model_dump() if hasattr(analysis_data, 'model_dump') else analysis_data
            # Insert match
            upsert_matching_result(MatchingResultCreate(
                position_id=position_id,
                candidate_id=candidate_id,
                cosine_similarity=cosine_similarity,
                analysis_data=analysis_data_dict,
                last_position_update=position_updated_at,
                last_candidate_update=candidate_updated_at,
                created_by=created_by,
                updated_by=updated_by
            ))
        # for the rests, insert cosine similarity
        for position in results[threshold_match:]:
            position_id = position['id']
            cosine_similarity = position['cosine_similarity']
            position_updated_at = position['updated_at']
            insert_cosine_similarity(CosineSimilarity(
                position_id=position_id,
                candidate_id=candidate_id,
                cosine_similarity=cosine_similarity,
                created_by=created_by,
                updated_by=updated_by
            ))


# Generate smart match for position id and candidate id
def generate_smart_match(position_id: str, candidate_id: str, created_by: str, updated_by: str, consine_similarity: float) -> None:
    with get_cursor() as cur:
        # Get position embedding
        cur.execute(
            "SELECT embedding, to_be_embebbed, updated_at FROM positions_smarthr WHERE id=%s",
            (position_id,),
        )
        position_result = cur.fetchone()
        if not position_result:
            return
        position_embedding = position_result['embedding']
        position_txt = position_result['to_be_embebbed']
        position_updated_at = position_result['updated_at']
        if not position_embedding:
            return
        # Get candidate embedding
        cur.execute(
            "SELECT embedding, to_be_embebbed, updated_at FROM candidates_smarthr WHERE id=%s",
            (candidate_id,),
        )
        candidate_result = cur.fetchone()
        if not candidate_result:
            return
        candidate_embedding = candidate_result['embedding']
        candidate_txt = candidate_result['to_be_embebbed']
        candidate_updated_at = candidate_result['updated_at']
        if not candidate_embedding:
            return
        # Generate match
        analysis_data = match_functions.get_candidate_analysis_custom_prompt(
            candidate_txt, position_txt
        )
        # Convert CompatibilityEvaluation to dict
        analysis_data_dict = analysis_data.model_dump() if hasattr(analysis_data, 'model_dump') else analysis_data
        # Insert match
        upsert_matching_result(MatchingResultCreate(
            position_id=position_id,
            candidate_id=candidate_id,
            cosine_similarity=consine_similarity,
            analysis_data=analysis_data_dict,
            last_position_update=position_updated_at,
            last_candidate_update=candidate_updated_at,
            created_by=created_by,
            updated_by=updated_by
        ))


# Generate smart match and cosine similarity for position id and candidate id
def generate_smart_match_and_cosine_similarity(position_id: str, candidate_id: str, created_by: str, updated_by: str) -> None:
    with get_cursor() as cur:
        # Check if both embeddings exist before comparing
        cur.execute(
            """
            SELECT
                p.embedding as position_embedding,
                c.embedding as candidate_embedding
            FROM positions_smarthr p, candidates_smarthr c
            WHERE p.id = %s AND c.id = %s
            AND p.embedding IS NOT NULL AND c.embedding IS NOT NULL
            """,
            (position_id, candidate_id),
        )
        result = cur.fetchone()
        if not result:
            return

        # Calculate cosine similarity
        cur.execute(
            "SELECT 1 - (%s::vector <=> %s::vector) AS cosine_similarity",
            (result['position_embedding'], result['candidate_embedding']),
        )
        cosine_similarity = cur.fetchone()['cosine_similarity']
        generate_smart_match(position_id, candidate_id, created_by, updated_by, cosine_similarity)


# Check if position update_at is more recent than last_position_update in matching_result, if so generate new smart match
def check_and_generate_smart_match_for_position(position_id: str, last_position_update: str, created_by) -> None:
    # Get updated_at from positions_smarthr
    with get_cursor() as cur:
        cur.execute(
            "SELECT updated_at FROM positions_smarthr WHERE id=%s",
            (position_id,),
        )
        position_updated_at = cur.fetchone()['updated_at']
        # check if position_updated_at is different from last_position_update
        if position_updated_at != last_position_update:
            generate_match_for_position(position_id, created_by, created_by)


# Check if candidate update_at is more recent than last_candidate_update in matching_result, if so generate new smart match
def check_and_generate_smart_match_for_candidate(candidate_id: str, last_candidate_update: str, created_by) -> None:
    # Get updated_at from candidates_smarthr
    with get_cursor() as cur:
        cur.execute(
            "SELECT updated_at FROM candidates_smarthr WHERE id=%s",
            (candidate_id,),
        )
        candidate_updated_at = cur.fetchone()['updated_at']
        # check if candidate_updated_at is different from last_candidate_update
        if candidate_updated_at != last_candidate_update:
            generate_match_for_candidate(candidate_id, created_by, created_by)
            return True
    return False
