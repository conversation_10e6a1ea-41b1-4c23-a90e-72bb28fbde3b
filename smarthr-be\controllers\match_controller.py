# match_controller.py
from datetime import datetime
import logging
import os
from typing import List

import psycopg2
from controllers.candidates_controller import get_candidate_by_id
from controllers.positions_controller import get_position_by_id
from models.matching_result import MatchedCandidate, MatchedPosition, MatchingPositionsResponse, MatchingResults
from services.match_service import matching_results_by_position, matching_results_by_candidate
from fastapi import HTTPException

# Telemetry Section
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

threshold_match = os.getenv("MAXIMUM_NUMBER_OF_MATCHES", 50)


# Get matching results by position_id
def get_matching_results_by_position(position_id: str, limit: int, created_by: str, updated_by: str) -> MatchingResults:
    try:
        # Check if position exists
        position = get_position_by_id(position_id)
        if not position:
            raise HTTPException(status_code=404, detail="Position not found")
        # if limit is 0, or less than 0, or greater than 100, set it to threshold_match
        limit = int(limit)
        if limit <= 0 or limit > 100:
            limit = int(threshold_match)
        # Get all results and then apply limit
        results = matching_results_by_position(position_id, created_by, updated_by)        
        return convert_to_positions_response(results, position.position_info, position.proj_id, limit)
    except psycopg2.Error as e:
        logger.error(f"Database error occurred: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Database error occurred: {str(e)}")
    except HTTPException as e:
        logger.error(f"HTTPException occurred: {str(e.detail)}")
        raise e
    except Exception as e:
        logger.error(f"Error occurred: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error occurred: {str(e)}")


# Get matching results by candidate_id
def get_matching_results_by_candidate(candidate_id: str, limit: int, created_by: str, updated_by: str) -> MatchingResults:
    try:
        # Check if candidate exists
        candidate = get_candidate_by_id(candidate_id)
        if not candidate:
            raise HTTPException(status_code=404, detail="Candidate not found")
        # if limit is 0, or less than 0, or greater than 100, set it to threshold_match
        limit = int(limit)
        if limit <= 0 or limit > 100:
            limit = int(threshold_match)
        # Get all results and then apply limit
        results = matching_results_by_candidate(candidate_id, created_by, updated_by)
        return convert_to_candidates_response(results, limit)
    except psycopg2.Error as e:
        logger.error(f"Database error occurred: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Database error occurred: {str(e)}")
    except HTTPException as e:
        logger.error(f"HTTPException occurred: {str(e.detail)}")
        raise e
    except Exception as e:
        logger.error(f"Error occurred: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error occurred: {str(e)}")


# Convert from list of dicts to MatchingPositionsResponse
def convert_to_positions_response(results: MatchingResults, processed_position: str, proj_id: str, limit: int) -> MatchingPositionsResponse:
    matched_positions = []
    for result in results.results[:limit]:
        matched_positions.append(
            MatchedPosition(
                id=result.position_id,
                proj_id=proj_id,
                candidate_info=result.info,
                cosine_similarity=result.cosine_similarity,
                analysis_data=result.analysis_data
            )
        )
    return MatchingPositionsResponse(
        matched_positions=matched_positions,
        processed_position=processed_position,
        timestamp=datetime.now(),
        total_items=results.total_items
    )


# Convert from MatchingResults to List[MatchedCandidate]
def convert_to_candidates_response(results: MatchingResults, limit: int) -> List[MatchedCandidate]:
    matched_candidates = []
    for result in results.results[:limit]:
        matched_candidates.append(
            MatchedCandidate(
                candidate_id=result.candidate_id,
                cosine_similarity=result.cosine_similarity,
                analysis_data=result.analysis_data,
                pos_clientName=result.info.get("clientName", "N/A"),
                pos_info_positionName=result.info.get("positionName", "N/A"),
                pos_info_roleName=result.info.get("roleName", "N/A"),
                pos_info_seniority=result.info.get("seniority", {}).get("name", "N/A"),
                pos_positionAllocations=result.info.get("positionAllocations", []),
                pos_projectName=result.info.get("projectName", "N/A"),
                position_id=result.position_id
            )
        )
    return matched_candidates
