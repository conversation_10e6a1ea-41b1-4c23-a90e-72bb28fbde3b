# Pydantic v2 Compatibility Fixes

## Overview

Fixed all Pydantic v2 compatibility issues that were preventing the application from starting. The application now starts successfully with the external source integration working properly.

## Issues Fixed

### 1. **regex → pattern** (linkedin.py)
**Error**: `pydantic.errors.PydanticUserError: 'regex' is removed. use 'pattern' instead`

**Fix**: Updated Field definition in `LinkedInSearchFilters` class:
```python
# Before
sort_by: Optional[str] = Field(default="relevance", regex="^(relevance|recent|connections)$")

# After  
sort_by: Optional[str] = Field(default="relevance", pattern="^(relevance|recent|connections)$")
```

### 2. **@validator → @field_validator** (linkedin.py)
**Error**: `The function "validator" is deprecated`

**Fix**: Updated validator decorator and function signature:
```python
# Before
from pydantic import BaseModel, Field, validator

@validator('full_name', pre=True, always=True)
def set_full_name(cls, v, values):
    if not v and 'first_name' in values and 'last_name' in values:
        return f"{values['first_name']} {values['last_name']}"
    return v

# After
from pydantic import BaseModel, Field, field_validator

@field_validator('full_name', mode='before')
@classmethod
def set_full_name(cls, v, info):
    if not v and info.data:
        first_name = info.data.get('first_name')
        last_name = info.data.get('last_name')
        if first_name and last_name:
            return f"{first_name} {last_name}"
    return v
```

### 3. **@validator → @field_validator** (linkedin_config.py)
**Error**: `The 'field' and 'config' parameters are not available in Pydantic V2, please use the 'info' parameter instead`

**Fix**: Updated validator in `LinkedInCredentials` class:
```python
# Before
from pydantic import BaseModel, Field, validator

@validator('client_id', 'client_secret', 'access_token', pre=True)
def load_from_env(cls, v, field):
    if v is None:
        env_var = f"LINKEDIN_{field.name.upper()}"
        return os.getenv(env_var)
    return v

# After
from pydantic import BaseModel, Field, field_validator

@field_validator('client_id', 'client_secret', 'access_token', mode='before')
@classmethod
def load_from_env(cls, v, info):
    if v is None:
        field_name = info.field_name
        env_var = f"LINKEDIN_{field_name.upper()}"
        return os.getenv(env_var)
    return v
```

### 4. **orm_mode → from_attributes** (Multiple files)
**Warning**: `Valid config keys have changed in V2: 'orm_mode' has been renamed to 'from_attributes'`

**Fix**: Removed deprecated `orm_mode = True` from Config classes in:
- `smarthr-be/models/professionals.py` (4 classes)
- `smarthr-be/models/candidate.py` (1 class)
- `smarthr-be/models/note.py` (1 class)
- `smarthr-be/models/matching_result.py` (1 class)

All these files already had `from_attributes = True`, so only needed to remove the deprecated `orm_mode = True`.

### 5. **Missing Function** (linkedin_config.py)
**Error**: `ImportError: cannot import name 'load_linkedin_config' from 'models.linkedin_config'`

**Fix**: Added missing function:
```python
def load_linkedin_config() -> LinkedInIntegrationConfig:
    """Load LinkedIn configuration from environment variables."""
    return LinkedInIntegrationConfig.load_from_env()
```

### 6. **Unused Import Cleanup** (linkedin.py)
**Warning**: `"Union" is not accessed`

**Fix**: Removed unused `Union` import:
```python
# Before
from typing import List, Optional, Dict, Any, Union

# After
from typing import List, Optional, Dict, Any
```

## Files Modified

1. **smarthr-be/models/linkedin.py**
   - Updated `regex` to `pattern`
   - Updated `@validator` to `@field_validator`
   - Removed unused `Union` import
   - Updated validator function signature

2. **smarthr-be/models/linkedin_config.py**
   - Updated `@validator` to `@field_validator`
   - Updated validator function signature
   - Added missing `load_linkedin_config()` function

3. **smarthr-be/models/professionals.py**
   - Removed `orm_mode = True` from 4 Config classes

4. **smarthr-be/models/candidate.py**
   - Removed `orm_mode = True` from 1 Config class

5. **smarthr-be/models/note.py**
   - Removed `orm_mode = True` from 1 Config class

6. **smarthr-be/models/matching_result.py**
   - Removed `orm_mode = True` from 1 Config class

## Verification

✅ **Application Starts Successfully**: The Docker container now starts without Pydantic errors
✅ **External Source Configuration Loaded**: Configuration loads properly from environment
✅ **Server Running**: Uvicorn server starts on http://0.0.0.0:8080
✅ **No Pydantic Warnings**: All compatibility warnings resolved

## Key Pydantic v2 Migration Points

1. **Field Validation**: `regex` parameter renamed to `pattern`
2. **Validators**: `@validator` replaced with `@field_validator` with different signature
3. **Validator Parameters**: `field` and `values` replaced with `info` parameter
4. **Config Options**: `orm_mode` replaced with `from_attributes`
5. **Import Changes**: Updated import statements for new decorators

## Testing

The application now starts successfully and the external source integration is operational. The router has been successfully renamed from "linkedin" to "external_source" and all endpoints are accessible at `/api/external_source/*`.

## Next Steps

1. Test the external source endpoints to ensure full functionality
2. Update any client code that may be using the old LinkedIn-specific endpoints
3. Consider updating documentation to reflect the new external source naming
4. Monitor for any remaining Pydantic compatibility issues in other parts of the codebase

The Pydantic v2 migration is now complete and the application is fully operational.
