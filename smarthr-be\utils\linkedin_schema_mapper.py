import logging
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime
import re

from models.linkedin import LinkedInProfile, LinkedInExperience, LinkedInEducation, LinkedInSkill

logger = logging.getLogger(__name__)


class LinkedInSchemaMapper:
    """Maps LinkedIn profile data to smartHR candidate schema."""
    
    def __init__(self):
        self.field_mappings = self._initialize_field_mappings()
        self.transformation_rules = self._initialize_transformation_rules()
    
    def _initialize_field_mappings(self) -> Dict[str, str]:
        """Initialize direct field mappings from LinkedIn to smartHR."""
        return {
            # Basic information
            "first_name": "first_name",
            "last_name": "last_name",
            "headline": "current_position",
            "summary": "professional_summary",
            
            # Contact information (usually not available from LinkedIn API)
            # These will be handled as special cases
            
            # Arrays that need transformation
            "experience": "experience",
            "education": "education",
            "skills": "skills",
            
            # Computed fields
            "profile_url": "linkedin_url",
            "location.name": "location"
        }
    
    def _initialize_transformation_rules(self) -> Dict[str, Callable]:
        """Initialize transformation rules for complex fields."""
        return {
            "first_name": self._transform_name,
            "last_name": self._transform_name,
            "current_position": self._transform_headline,
            "professional_summary": self._transform_summary,
            "location": self._transform_location,
            "experience": self._transform_experience_array,
            "education": self._transform_education_array,
            "skills": self._transform_skills_array,
            "linkedin_url": self._transform_linkedin_url
        }
    
    def map_profile_to_smarthr(self, linkedin_profile: LinkedInProfile) -> Dict[str, Any]:
        """Map LinkedIn profile to smartHR candidate format."""
        try:
            smarthr_candidate = {}
            
            # Apply direct mappings
            for linkedin_field, smarthr_field in self.field_mappings.items():
                value = self._get_nested_value(linkedin_profile.model_dump(), linkedin_field)
                
                if value is not None:
                    # Apply transformation rule if exists
                    if smarthr_field in self.transformation_rules:
                        transformed_value = self.transformation_rules[smarthr_field](
                            value, linkedin_profile
                        )
                        smarthr_candidate[smarthr_field] = transformed_value
                    else:
                        smarthr_candidate[smarthr_field] = value
            
            # Handle required fields that might be missing
            smarthr_candidate = self._ensure_required_fields(smarthr_candidate, linkedin_profile)
            
            # Add computed fields
            smarthr_candidate = self._add_computed_fields(smarthr_candidate, linkedin_profile)
            
            return smarthr_candidate
            
        except Exception as e:
            logger.error(f"Failed to map LinkedIn profile to smartHR: {str(e)}")
            return self._create_minimal_candidate(linkedin_profile)
    
    def _get_nested_value(self, data: Dict[str, Any], field_path: str) -> Any:
        """Get value from nested dictionary using dot notation."""
        try:
            keys = field_path.split('.')
            value = data
            
            for key in keys:
                if isinstance(value, dict) and key in value:
                    value = value[key]
                else:
                    return None
            
            return value
            
        except Exception:
            return None
    
    def _transform_name(self, value: Any, profile: LinkedInProfile) -> str:
        """Transform name fields."""
        if isinstance(value, str):
            # Clean and normalize name
            name = value.strip()
            name = re.sub(r'\s+', ' ', name)  # Remove extra spaces
            name = name.title()  # Proper case
            return name
        
        return str(value) if value else "Unknown"
    
    def _transform_headline(self, value: Any, profile: LinkedInProfile) -> str:
        """Transform headline to current position."""
        if not value:
            return "not_provided"
        
        headline = str(value)
        
        # Extract job title from headline (before " at " or " | ")
        patterns = [r'^([^|]+)\s*\|\s*', r'^([^@]+)\s*@\s*', r'^([^-]+)\s*-\s*']
        
        for pattern in patterns:
            match = re.match(pattern, headline)
            if match:
                title = match.group(1).strip()
                if title and len(title) > 2:
                    return title
        
        # If no pattern matches, return the full headline
        return headline
    
    def _transform_summary(self, value: Any, profile: LinkedInProfile) -> str:
        """Transform summary to professional summary."""
        if value:
            return str(value)
        
        # Generate summary from headline if summary is missing
        if profile.headline:
            return f"Professional with experience as {profile.headline}"
        
        return "not_provided"
    
    def _transform_location(self, value: Any, profile: LinkedInProfile) -> str:
        """Transform location information."""
        if isinstance(value, str):
            return value
        
        # Handle location object
        if hasattr(profile, 'location') and profile.location:
            location_parts = []
            
            if profile.location.city:
                location_parts.append(profile.location.city)
            
            if profile.location.region:
                location_parts.append(profile.location.region)
            
            if profile.location.name:
                return profile.location.name
            
            if location_parts:
                return ", ".join(location_parts)
        
        return "not_provided"
    
    def _transform_experience_array(self, value: Any, profile: LinkedInProfile) -> List[Dict[str, Any]]:
        """Transform experience array."""
        if not isinstance(value, list):
            return []
        
        transformed_experience = []
        
        for exp in value:
            if isinstance(exp, LinkedInExperience):
                exp_dict = exp.model_dump()
            elif isinstance(exp, dict):
                exp_dict = exp
            else:
                continue
            
            transformed_exp = {
                "title": exp_dict.get("title", "Unknown Position"),
                "company": self._extract_company_name(exp_dict.get("company")),
                "location": self._extract_experience_location(exp_dict.get("location")),
                "start_date": self._format_date(exp_dict.get("start_date")),
                "end_date": self._format_date(exp_dict.get("end_date")),
                "is_current": exp_dict.get("is_current", False),
                "description": exp_dict.get("description", "")
            }
            
            transformed_experience.append(transformed_exp)
        
        return transformed_experience
    
    def _transform_education_array(self, value: Any, profile: LinkedInProfile) -> List[Dict[str, Any]]:
        """Transform education array."""
        if not isinstance(value, list):
            return []
        
        transformed_education = []
        
        for edu in value:
            if isinstance(edu, LinkedInEducation):
                edu_dict = edu.model_dump()
            elif isinstance(edu, dict):
                edu_dict = edu
            else:
                continue
            
            transformed_edu = {
                "school": edu_dict.get("school", "Unknown School"),
                "degree": edu_dict.get("degree", ""),
                "field_of_study": edu_dict.get("field_of_study", ""),
                "start_year": edu_dict.get("start_year"),
                "end_year": edu_dict.get("end_year")
            }
            
            transformed_education.append(transformed_edu)
        
        return transformed_education
    
    def _transform_skills_array(self, value: Any, profile: LinkedInProfile) -> List[str]:
        """Transform skills array."""
        if not isinstance(value, list):
            return []
        
        skills = []
        
        for skill in value:
            if isinstance(skill, LinkedInSkill):
                skills.append(skill.name)
            elif isinstance(skill, dict):
                skills.append(skill.get("name", ""))
            elif isinstance(skill, str):
                skills.append(skill)
        
        # Remove empty skills and duplicates
        skills = [skill for skill in skills if skill and skill.strip()]
        return list(dict.fromkeys(skills))  # Remove duplicates while preserving order
    
    def _transform_linkedin_url(self, value: Any, profile: LinkedInProfile) -> str:
        """Transform to LinkedIn URL."""
        if isinstance(value, str) and value.startswith("http"):
            return value
        
        if profile.public_id:
            return f"https://www.linkedin.com/in/{profile.public_id}"
        
        return "not_provided"
    
    def _extract_company_name(self, company_data: Any) -> str:
        """Extract company name from company data."""
        if isinstance(company_data, str):
            return company_data
        
        if isinstance(company_data, dict):
            return company_data.get("name", "Unknown Company")
        
        if hasattr(company_data, 'name'):
            return company_data.name
        
        return "Unknown Company"
    
    def _extract_experience_location(self, location_data: Any) -> str:
        """Extract location from experience location data."""
        if isinstance(location_data, str):
            return location_data
        
        if isinstance(location_data, dict):
            return location_data.get("name", "")
        
        if hasattr(location_data, 'name'):
            return location_data.name
        
        return ""
    
    def _format_date(self, date_data: Any) -> Optional[str]:
        """Format date information."""
        if not date_data:
            return None
        
        if isinstance(date_data, str):
            return date_data
        
        if isinstance(date_data, dict):
            year = date_data.get("year")
            month = date_data.get("month")
            
            if year and month:
                return f"{year}-{month:02d}"
            elif year:
                return str(year)
        
        return str(date_data)
    
    def _ensure_required_fields(self, candidate: Dict[str, Any], profile: LinkedInProfile) -> Dict[str, Any]:
        """Ensure all required fields are present."""
        required_fields = {
            "first_name": profile.first_name or "Unknown",
            "last_name": profile.last_name or "Unknown",
            "email": "not_provided",
            "phone": "not_provided",
            "location": "not_provided",
            "current_position": "not_provided",
            "professional_summary": "not_provided",
            "experience": [],
            "education": [],
            "skills": []
        }
        
        for field, default_value in required_fields.items():
            if field not in candidate or not candidate[field]:
                candidate[field] = default_value
        
        return candidate
    
    def _add_computed_fields(self, candidate: Dict[str, Any], profile: LinkedInProfile) -> Dict[str, Any]:
        """Add computed fields to candidate."""
        # Add metadata
        candidate["source"] = "linkedin"
        candidate["imported_at"] = datetime.now().isoformat()
        candidate["linkedin_profile_id"] = profile.id
        
        # Compute additional fields if possible
        if profile.connections_count:
            candidate["linkedin_connections"] = profile.connections_count
        
        if profile.followers_count:
            candidate["linkedin_followers"] = profile.followers_count
        
        return candidate
    
    def _create_minimal_candidate(self, profile: LinkedInProfile) -> Dict[str, Any]:
        """Create minimal candidate as fallback."""
        return {
            "first_name": profile.first_name or "Unknown",
            "last_name": profile.last_name or "Unknown",
            "email": "not_provided",
            "phone": "not_provided",
            "location": "not_provided",
            "current_position": profile.headline or "not_provided",
            "professional_summary": "not_provided",
            "experience": [],
            "education": [],
            "skills": [],
            "linkedin_url": f"https://www.linkedin.com/in/{profile.public_id}" if profile.public_id else "not_provided",
            "source": "linkedin",
            "imported_at": datetime.now().isoformat(),
            "linkedin_profile_id": profile.id
        }


# Convenience function
def map_linkedin_profile_to_smarthr(profile: LinkedInProfile) -> Dict[str, Any]:
    """Map LinkedIn profile to smartHR candidate format."""
    mapper = LinkedInSchemaMapper()
    return mapper.map_profile_to_smarthr(profile)
