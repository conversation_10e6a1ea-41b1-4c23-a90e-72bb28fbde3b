#!/usr/bin/env python3
"""Simple validation script for the simplified LinkedIn endpoints."""

def main():
    try:
        # Test basic import
        from routes.routes_linkedin import router, LinkedInSearchRequest, LinkedInTransformRequest
        
        # Check router configuration
        print(f"Router prefix: {router.prefix}")
        print(f"Router tags: {router.tags}")
        print(f"Number of routes: {len(router.routes)}")
        
        # List all routes
        for route in router.routes:
            print(f"  - {route.methods} {route.path}")
        
        # Test model creation
        search_req = LinkedInSearchRequest(keywords=["test"])
        print(f"LinkedInSearchRequest created successfully")
        
        transform_req = LinkedInTransformRequest(profiles=[{"first_name": "Test", "last_name": "User"}])
        print(f"LinkedInTransformRequest created successfully")
        
        print("✅ All validations passed!")
        return True
        
    except Exception as e:
        print(f"❌ Validation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
