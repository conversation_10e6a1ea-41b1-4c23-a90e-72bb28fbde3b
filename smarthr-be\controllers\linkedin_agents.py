import logging
import time
import uuid
import json
from datetime import datetime
from typing import Dict, List, Optional, Any
from abc import ABC, abstractmethod

# Lang<PERSON>hain imports
from langchain_core.messages import HumanMessage

# Internal imports
from models.linkedin import (
    LinkedInAgent1Request,
    LinkedInAgent1Response,
    LinkedInSearchRequest,
    LinkedInSearchResponse,
    LinkedInProfile,
    LinkedInAPIError,
    AgentError,
    SchemaTransformationRequest,
    SchemaTransformationResponse,
    TransformedCandidate
)
from models.linkedin_config import LinkedInIntegrationConfig, get_active_linkedin_config
from models.llm import inference_with_fallback, get_related_class_definitions
from models.candidate import Candidate
from utils.linkedin_client import LinkedInAPIClient
from utils.linkedin_search_builder import (
    validate_linkedin_search_filters,
    sanitize_linkedin_search_filters,
    build_optimized_linkedin_search
)
from utils.linkedin_schema_mapper import map_linkedin_profile_to_smarthr
from utils.linkedin_transformation_validator import (
    validate_smarthr_candidate,
    ValidationSeverity
)

# Setup logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


class BaseLinkedInAgent(ABC):
    """Base class for LinkedIn integration agents following smartHR patterns."""
    
    def __init__(self, agent_id: str, config: Optional[LinkedInIntegrationConfig] = None):
        self.agent_id = agent_id
        self.config = config or get_active_linkedin_config()
        self.logger = logging.getLogger(f"{__name__}.{agent_id}")
        self.logger.setLevel(logging.DEBUG)
        
        # Performance tracking
        self.start_time: Optional[float] = None
        self.processing_stats = {
            "requests_processed": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "total_processing_time_ms": 0,
            "average_processing_time_ms": 0
        }
        
        self.logger.info(f"Initialized {agent_id} with configuration")
    
    def _start_processing(self) -> None:
        """Start processing timer."""
        self.start_time = time.time()
    
    def _end_processing(self) -> int:
        """End processing timer and return elapsed time in milliseconds."""
        if self.start_time is None:
            return 0
        
        elapsed_ms = int((time.time() - self.start_time) * 1000)
        self.start_time = None
        
        # Update stats
        self.processing_stats["requests_processed"] += 1
        self.processing_stats["total_processing_time_ms"] += elapsed_ms
        self.processing_stats["average_processing_time_ms"] = (
            self.processing_stats["total_processing_time_ms"] / 
            self.processing_stats["requests_processed"]
        )
        
        return elapsed_ms
    
    def _log_success(self, message: str, **kwargs) -> None:
        """Log successful operation."""
        self.processing_stats["successful_requests"] += 1
        self.logger.info(f"{self.agent_id.upper()}: {message}", extra=kwargs)
    
    def _log_error(self, message: str, error: Optional[Exception] = None, **kwargs) -> None:
        """Log error operation."""
        self.processing_stats["failed_requests"] += 1
        if error:
            self.logger.error(f"{self.agent_id.upper()}: {message} - {str(error)}", extra=kwargs)
        else:
            self.logger.error(f"{self.agent_id.upper()}: {message}", extra=kwargs)
    
    def _create_agent_error(self, error_type: str, error_message: str, 
                           error_details: Optional[Dict[str, Any]] = None,
                           recoverable: bool = True) -> AgentError:
        """Create standardized agent error."""
        return AgentError(
            agent_id=self.agent_id,
            error_type=error_type,
            error_message=error_message,
            error_details=error_details or {},
            recoverable=recoverable,
            suggested_action=self._get_suggested_action(error_type)
        )
    
    def _get_suggested_action(self, error_type: str) -> Optional[str]:
        """Get suggested action for error type."""
        suggestions = {
            "rate_limit_exceeded": "Wait for rate limit reset or reduce request frequency",
            "authentication_failed": "Check LinkedIn credentials and refresh tokens",
            "network_error": "Check network connectivity and retry request",
            "parsing_error": "Validate input data format and schema",
            "validation_error": "Review request parameters and fix validation issues",
            "timeout_error": "Increase timeout settings or retry with smaller batch size"
        }
        return suggestions.get(error_type, "Review error details and retry operation")
    
    def get_agent_stats(self) -> Dict[str, Any]:
        """Get agent performance statistics."""
        return {
            "agent_id": self.agent_id,
            "stats": self.processing_stats.copy(),
            "config_summary": {
                "provider": self.config.api_config.provider,
                "rate_limit_rpm": self.config.api_config.rate_limits.requests_per_minute,
                "timeout_seconds": self.config.agent_config.agent_timeout_seconds
            }
        }
    
    @abstractmethod
    def process_request(self, request: Any) -> Any:
        """Process agent-specific request. Must be implemented by subclasses."""
        pass
    
    def health_check(self) -> Dict[str, Any]:
        """Perform agent health check."""
        try:
            # Basic configuration validation
            config_issues = self.config.validate_config()
            
            health_status = {
                "agent_id": self.agent_id,
                "status": "healthy" if not config_issues else "unhealthy",
                "timestamp": datetime.now().isoformat(),
                "config_issues": config_issues,
                "stats": self.processing_stats.copy()
            }
            
            if config_issues:
                self._log_error(f"Health check failed: {', '.join(config_issues)}")
            else:
                self.logger.info(f"{self.agent_id.upper()}: Health check passed")
            
            return health_status
            
        except Exception as e:
            self._log_error("Health check failed with exception", e)
            return {
                "agent_id": self.agent_id,
                "status": "error",
                "timestamp": datetime.now().isoformat(),
                "error": str(e)
            }


class LinkedInSearchAgent(BaseLinkedInAgent):
    """Agent 1: LinkedIn Search Agent for candidate discovery."""
    
    def __init__(self, config: Optional[LinkedInIntegrationConfig] = None):
        super().__init__("linkedin_search_agent", config)
        
        # Initialize LinkedIn client (will be implemented in next phase)
        self.linkedin_client = None
        self._initialize_client()
    
    def _initialize_client(self) -> None:
        """Initialize LinkedIn API client based on configuration."""
        try:
            provider = self.config.api_config.provider
            self.logger.info(f"Initializing LinkedIn client with provider: {provider}")

            # Initialize the LinkedIn API client
            self.linkedin_client = LinkedInAPIClient(self.config)

            self.logger.info("LinkedIn client initialized successfully")

        except Exception as e:
            self._log_error("Failed to initialize LinkedIn client", e)
            raise
    
    async def process_request(self, request: LinkedInAgent1Request) -> LinkedInAgent1Response:
        """Process LinkedIn search request."""
        self._start_processing()
        
        try:
            self.logger.info(f"Processing LinkedIn search request: {request.search_request.filters.keywords}")
            
            # Validate request
            if not self._validate_search_request(request.search_request):
                error = self._create_agent_error(
                    "validation_error",
                    "Invalid search request parameters",
                    {"request_id": request.search_request.search_id}
                )
                return LinkedInAgent1Response(
                    search_results=LinkedInSearchResponse(
                        search_id=str(uuid.uuid4()),
                        total_results=0,
                        returned_results=0,
                        profiles=[],
                        search_filters_used=request.search_request.filters,
                        search_metadata={"error": "validation_error", "request_id": request.search_request.search_id},
                        success=False,
                        error_message=error.error_message
                    ),
                    success=False,
                    error_message=error.error_message,
                    processing_time_ms=self._end_processing()
                )
            
            # Sanitize and optimize search parameters
            optimized_request = self._optimize_search_request(request.search_request)

            # Perform LinkedIn search
            search_results = await self._perform_linkedin_search(optimized_request)
            
            processing_time = self._end_processing()
            
            self._log_success(
                f"LinkedIn search completed successfully",
                total_results=search_results.total_results,
                returned_results=search_results.returned_results,
                processing_time_ms=processing_time
            )
            
            return LinkedInAgent1Response(
                search_results=search_results,
                success=True,
                processing_time_ms=processing_time
            )
            
        except Exception as e:
            processing_time = self._end_processing()
            self._log_error("LinkedIn search failed", e)

            # Don't duplicate error message if it's already a LinkedInAPIError
            if isinstance(e, LinkedInAPIError):
                error_message = e.error_message
            else:
                error_message = f"LinkedIn search operation failed: {str(e)}"

            error = self._create_agent_error(
                "search_error",
                error_message,
                {"exception_type": type(e).__name__}
            )
            
            return LinkedInAgent1Response(
                search_results=LinkedInSearchResponse(
                    search_id=str(uuid.uuid4()),
                    total_results=0,
                    returned_results=0,
                    profiles=[],
                    search_filters_used=request.search_request.filters,
                    search_metadata={"error": "search_failed", "exception": str(e)},
                    success=False,
                    error_message=error.error_message
                ),
                success=False,
                error_message=error.error_message,
                processing_time_ms=processing_time
            )
    
    def _validate_search_request(self, search_request: LinkedInSearchRequest) -> bool:
        """Validate LinkedIn search request parameters."""
        try:
            # Use the comprehensive validation system
            is_valid, validation_errors = validate_linkedin_search_filters(
                search_request.filters, self.config
            )

            if not is_valid:
                for error in validation_errors:
                    self.logger.warning(f"Search validation error: {error}")
                return False

            return True

        except Exception as e:
            self._log_error("Search request validation failed", e)
            return False

    def _optimize_search_request(self, search_request: LinkedInSearchRequest) -> LinkedInSearchRequest:
        """Optimize search request for better results."""
        try:
            # Build optimized search query and filters
            search_query, advanced_filters, optimized_filters = build_optimized_linkedin_search(
                search_request.filters, self.config
            )

            # Create optimized request
            optimized_request = LinkedInSearchRequest(
                search_type=search_request.search_type,
                filters=optimized_filters,
                project_id=search_request.project_id,
                requested_by=search_request.requested_by,
                search_id=search_request.search_id
            )

            self.logger.info(f"Search request optimized: query='{search_query}', filters={len(advanced_filters)}")
            return optimized_request

        except Exception as e:
            self._log_error("Search request optimization failed", e)
            return search_request  # Return original request as fallback
    
    async def _perform_linkedin_search(self, search_request: LinkedInSearchRequest) -> LinkedInSearchResponse:
        """Perform the actual LinkedIn search operation."""
        try:
            async with self.linkedin_client as client:
                search_results = await client.search_people(search_request)
                return search_results

        except LinkedInAPIError as e:
            self.logger.error(f"LinkedIn API error: {e.error_message}")
            raise
        except Exception as e:
            self.logger.error(f"Unexpected error during LinkedIn search: {str(e)}")
            raise LinkedInAPIError(
                error_code="unexpected_error",
                error_message=f"Unexpected error: {str(e)}"
            )
    
    async def search_candidates(self, filters: Dict[str, Any], limit: int = 25) -> LinkedInSearchResponse:
        """Convenience method for direct candidate searching."""
        from models.linkedin import LinkedInSearchFilters
        
        search_filters = LinkedInSearchFilters(**filters, limit=limit)
        search_request = LinkedInSearchRequest(
            filters=search_filters,
            requested_by="system"
        )
        
        agent_request = LinkedInAgent1Request(search_request=search_request)
        response = await self.process_request(agent_request)
        
        if response.success:
            return response.search_results
        else:
            raise Exception(f"LinkedIn search failed: {response.error_message}")


class SchemaTransformationAgent(BaseLinkedInAgent):
    """Agent 2: Schema Transformation Agent for converting LinkedIn data to smartHR format."""

    def __init__(self, config: Optional[LinkedInIntegrationConfig] = None):
        super().__init__("schema_transformation_agent", config)

        # Initialize transformation templates and validation rules
        self._initialize_transformation_rules()

    def _initialize_transformation_rules(self) -> None:
        """Initialize schema transformation rules and templates."""
        try:
            self.transformation_rules = self.config.transformation_config.field_mapping_rules
            self.required_fields = self.config.transformation_config.required_smarthr_fields
            self.optional_fields = self.config.transformation_config.optional_smarthr_fields

            self.logger.info("Schema transformation rules initialized successfully")

        except Exception as e:
            self._log_error("Failed to initialize transformation rules", e)
            raise

    def process_request(self, request: SchemaTransformationRequest) -> SchemaTransformationResponse:
        """Process schema transformation request."""
        self._start_processing()

        try:
            self.logger.info(f"Processing schema transformation for {len(request.linkedin_profiles)} profiles")

            # Validate request
            if not self._validate_transformation_request(request):
                error = self._create_agent_error(
                    "validation_error",
                    "Invalid transformation request parameters"
                )
                return SchemaTransformationResponse(
                    transformed_candidates=[],
                    success=False,
                    error_message=error.error_message,
                    processing_time_ms=self._end_processing()
                )

            # Transform profiles
            transformed_candidates = []
            for profile in request.linkedin_profiles:
                try:
                    transformed_candidate = self._transform_profile_to_smarthr(profile)
                    transformed_candidates.append(transformed_candidate)
                except Exception as e:
                    self.logger.warning(f"Failed to transform profile {profile.id}: {str(e)}")
                    # Continue with other profiles
                    continue

            processing_time = self._end_processing()

            self._log_success(
                f"Schema transformation completed",
                input_profiles=len(request.linkedin_profiles),
                successful_transformations=len(transformed_candidates),
                processing_time_ms=processing_time
            )

            return SchemaTransformationResponse(
                transformed_candidates=transformed_candidates,
                success=True,
                transformation_summary=f"Successfully transformed {len(transformed_candidates)} out of {len(request.linkedin_profiles)} profiles",
                processing_time_ms=processing_time
            )

        except Exception as e:
            processing_time = self._end_processing()
            self._log_error("Schema transformation failed", e)

            # Don't duplicate error message if it's already a structured error
            if hasattr(e, 'error_message'):
                error_message = e.error_message
            else:
                error_message = f"Schema transformation operation failed: {str(e)}"

            error = self._create_agent_error(
                "transformation_error",
                error_message,
                {"exception_type": type(e).__name__}
            )

            return SchemaTransformationResponse(
                transformed_candidates=[],
                success=False,
                error_message=error.error_message,
                processing_time_ms=processing_time
            )

    def _validate_transformation_request(self, request: SchemaTransformationRequest) -> bool:
        """Validate schema transformation request parameters."""
        try:
            if not request.linkedin_profiles:
                self.logger.warning("No LinkedIn profiles provided for transformation")
                return False

            if len(request.linkedin_profiles) > 100:  # Reasonable batch limit
                self.logger.warning(f"Too many profiles for transformation: {len(request.linkedin_profiles)}")
                return False

            return True

        except Exception as e:
            self._log_error("Transformation request validation failed", e)
            return False

    def _transform_profile_to_smarthr(self, linkedin_profile: LinkedInProfile) -> TransformedCandidate:
        """Transform a single LinkedIn profile to smartHR candidate format."""
        try:
            # Try LLM transformation first
            transformation_result = self._llm_transform_profile(linkedin_profile)

            if transformation_result:
                # Validate LLM transformation
                is_valid, validation_issues = validate_smarthr_candidate(transformation_result)

                # Calculate confidence based on validation
                confidence = self._calculate_validation_confidence(validation_issues)

                return TransformedCandidate(
                    candidate_info=transformation_result,
                    source_linkedin_profile=linkedin_profile,
                    transformation_confidence=confidence,
                    transformation_notes=self._create_transformation_notes(
                        "LLM-powered transformation", validation_issues
                    ),
                    missing_fields=self._extract_missing_fields(validation_issues),
                    additional_fields=[]
                )
            else:
                # Fallback to rule-based transformation
                transformed_candidate = self._rule_based_transform_profile(linkedin_profile)

                # Validate rule-based transformation
                is_valid, validation_issues = validate_smarthr_candidate(
                    transformed_candidate.candidate_info
                )

                # Update with validation results
                transformed_candidate.transformation_confidence = self._calculate_validation_confidence(
                    validation_issues
                )
                transformed_candidate.transformation_notes = self._create_transformation_notes(
                    transformed_candidate.transformation_notes, validation_issues
                )
                transformed_candidate.missing_fields = self._extract_missing_fields(validation_issues)

                return transformed_candidate

        except Exception as e:
            self.logger.error(f"Profile transformation failed for {linkedin_profile.id}: {str(e)}")
            # Return minimal transformation as fallback
            return self._minimal_transform_profile(linkedin_profile)

    def _llm_transform_profile(self, linkedin_profile: LinkedInProfile) -> Optional[Dict[str, Any]]:
        """Use LLM to intelligently transform LinkedIn profile to smartHR format."""
        try:
            # First, try rule-based transformation as baseline
            baseline_transformation = map_linkedin_profile_to_smarthr(linkedin_profile)

            task_prompt = """
            You are an expert HR data transformation specialist. Your task is to enhance and improve
            the LinkedIn to smartHR candidate transformation.

            CONTEXT:
            You will receive:
            1. Original LinkedIn profile data
            2. A baseline transformation already performed
            3. Target smartHR schema requirements

            YOUR OBJECTIVES:
            1. ENHANCE the baseline transformation with intelligent insights
            2. GENERATE missing professional summary from available context
            3. IMPROVE experience descriptions and skill extraction
            4. INFER additional relevant information from context
            5. ENSURE data quality and professional presentation

            ENHANCEMENT RULES:
            - If professional_summary is "not_provided", generate one from headline, experience, and skills
            - Enhance experience descriptions with industry-standard language
            - Prioritize and categorize skills by relevance and expertise level
            - Standardize location, company names, and job titles
            - Add relevant keywords for better searchability
            - Maintain accuracy - never fabricate information not present in source

            REQUIRED OUTPUT FORMAT:
            Return a JSON object with the enhanced smartHR candidate data structure.
            Include a "transformation_notes" field explaining key enhancements made.

            QUALITY STANDARDS:
            - Professional tone and language
            - Industry-standard terminology
            - Consistent formatting
            - No fabricated contact information
            - Preserve all original factual data
            """

            # Prepare comprehensive data for LLM
            transformation_context = {
                "original_linkedin_profile": linkedin_profile.dict(),
                "baseline_transformation": baseline_transformation,
                "target_schema_example": {
                    "first_name": "string",
                    "last_name": "string",
                    "email": "string (use 'not_provided' if unavailable)",
                    "phone": "string (use 'not_provided' if unavailable)",
                    "location": "string",
                    "current_position": "string",
                    "professional_summary": "string (generate if missing)",
                    "experience": [
                        {
                            "title": "string",
                            "company": "string",
                            "location": "string",
                            "start_date": "string",
                            "end_date": "string or null",
                            "is_current": "boolean",
                            "description": "string"
                        }
                    ],
                    "education": [
                        {
                            "school": "string",
                            "degree": "string",
                            "field_of_study": "string",
                            "start_year": "number",
                            "end_year": "number"
                        }
                    ],
                    "skills": ["array of strings"],
                    "linkedin_url": "string"
                },
                "enhancement_focus": [
                    "professional_summary_generation",
                    "experience_description_enhancement",
                    "skill_prioritization",
                    "data_standardization"
                ]
            }

            user_msg = HumanMessage(content=json.dumps(transformation_context, ensure_ascii=False, default=str))

            # Use existing LLM infrastructure with structured output
            from models.candidate import Candidate

            result = inference_with_fallback(
                task_prompt=task_prompt,
                model_schema=None,  # Free-form JSON for flexibility
                user_messages=[user_msg],
                models_order=self.config.transformation_config.llm_models_order
            )

            if result and hasattr(result, 'content'):
                try:
                    enhanced_data = json.loads(result.content)

                    # Validate that enhanced data has required structure
                    if self._validate_enhanced_transformation(enhanced_data, baseline_transformation):
                        self.logger.info("LLM enhancement successful")
                        return enhanced_data
                    else:
                        self.logger.warning("LLM enhancement validation failed, using baseline")
                        return baseline_transformation

                except json.JSONDecodeError as e:
                    self.logger.warning(f"LLM returned invalid JSON: {str(e)}, using baseline")
                    return baseline_transformation

            # Fallback to baseline transformation
            return baseline_transformation

        except Exception as e:
            self.logger.error(f"LLM transformation failed: {str(e)}")
            # Fallback to rule-based transformation
            return map_linkedin_profile_to_smarthr(linkedin_profile)

    def _validate_transformed_data(self, transformed_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate transformed candidate data and calculate confidence score."""
        try:
            missing_fields = []
            additional_fields = []
            confidence_factors = []

            # Check required fields
            for field in self.required_fields:
                if field not in transformed_data or not transformed_data[field]:
                    missing_fields.append(field)
                    confidence_factors.append(0.0)
                else:
                    confidence_factors.append(1.0)

            # Check for additional fields not in schema
            expected_fields = set(self.required_fields + self.optional_fields)
            for field in transformed_data.keys():
                if field not in expected_fields:
                    additional_fields.append(field)

            # Calculate confidence score
            if confidence_factors:
                confidence = sum(confidence_factors) / len(confidence_factors)
            else:
                confidence = 0.0

            # Adjust confidence based on data quality
            if transformed_data.get('email') == 'not_provided':
                confidence *= 0.9
            if transformed_data.get('phone') == 'not_provided':
                confidence *= 0.9

            notes = []
            if missing_fields:
                notes.append(f"Missing required fields: {', '.join(missing_fields)}")
            if additional_fields:
                notes.append(f"Additional fields found: {', '.join(additional_fields)}")

            return {
                "confidence": confidence,
                "missing_fields": missing_fields,
                "additional_fields": additional_fields,
                "notes": "; ".join(notes) if notes else "Transformation completed successfully"
            }

        except Exception as e:
            self.logger.error(f"Validation failed: {str(e)}")
            return {
                "confidence": 0.0,
                "missing_fields": self.required_fields,
                "additional_fields": [],
                "notes": f"Validation error: {str(e)}"
            }

    def _validate_enhanced_transformation(self, enhanced_data: Dict[str, Any],
                                        baseline_data: Dict[str, Any]) -> bool:
        """Validate that LLM enhancement maintains data integrity."""
        try:
            # Check that required fields are still present
            required_fields = ["first_name", "last_name", "current_position"]

            for field in required_fields:
                if field not in enhanced_data:
                    self.logger.warning(f"Enhanced data missing required field: {field}")
                    return False

                # Check that we didn't lose important data
                if (baseline_data.get(field) and baseline_data[field] != "not_provided"
                    and not enhanced_data.get(field)):
                    self.logger.warning(f"Enhanced data lost baseline value for: {field}")
                    return False

            # Validate array fields
            array_fields = ["experience", "education", "skills"]
            for field in array_fields:
                if field in enhanced_data:
                    if not isinstance(enhanced_data[field], list):
                        self.logger.warning(f"Enhanced data has invalid type for {field}")
                        return False

            # Check for reasonable data sizes
            if "skills" in enhanced_data and len(enhanced_data["skills"]) > 50:
                self.logger.warning("Enhanced data has unreasonably many skills")
                return False

            if "experience" in enhanced_data and len(enhanced_data["experience"]) > 20:
                self.logger.warning("Enhanced data has unreasonably many experience entries")
                return False

            return True

        except Exception as e:
            self.logger.error(f"Enhanced transformation validation failed: {str(e)}")
            return False

    def _calculate_validation_confidence(self, validation_issues: List) -> float:
        """Calculate confidence score based on validation issues."""
        try:
            if not validation_issues:
                return 0.95  # High confidence if no issues

            error_count = sum(1 for issue in validation_issues if issue.severity == ValidationSeverity.ERROR)
            warning_count = sum(1 for issue in validation_issues if issue.severity == ValidationSeverity.WARNING)
            info_count = sum(1 for issue in validation_issues if issue.severity == ValidationSeverity.INFO)

            # Start with base confidence
            confidence = 0.8

            # Reduce confidence based on issues
            confidence -= error_count * 0.2  # Errors significantly reduce confidence
            confidence -= warning_count * 0.1  # Warnings moderately reduce confidence
            confidence -= info_count * 0.02   # Info issues slightly reduce confidence

            # Ensure confidence stays within bounds
            return max(0.1, min(confidence, 1.0))

        except Exception as e:
            self.logger.error(f"Confidence calculation failed: {str(e)}")
            return 0.5

    def _create_transformation_notes(self, base_notes: str, validation_issues: List) -> str:
        """Create comprehensive transformation notes including validation results."""
        try:
            notes = [base_notes]

            if validation_issues:
                error_count = sum(1 for issue in validation_issues if issue.severity == ValidationSeverity.ERROR)
                warning_count = sum(1 for issue in validation_issues if issue.severity == ValidationSeverity.WARNING)
                info_count = sum(1 for issue in validation_issues if issue.severity == ValidationSeverity.INFO)

                validation_summary = f"Validation: {error_count} errors, {warning_count} warnings, {info_count} info"
                notes.append(validation_summary)

                # Add specific high-priority issues
                high_priority_issues = [
                    issue for issue in validation_issues
                    if issue.severity in [ValidationSeverity.ERROR, ValidationSeverity.WARNING]
                ][:3]  # Limit to top 3

                for issue in high_priority_issues:
                    notes.append(f"{issue.severity.upper()}: {issue.field} - {issue.message}")

            return "; ".join(notes)

        except Exception as e:
            self.logger.error(f"Notes creation failed: {str(e)}")
            return base_notes

    def _extract_missing_fields(self, validation_issues: List) -> List[str]:
        """Extract missing fields from validation issues."""
        try:
            missing_fields = []

            for issue in validation_issues:
                if (issue.severity == ValidationSeverity.ERROR and
                    ("missing" in issue.message.lower() or "not provided" in issue.message.lower())):
                    missing_fields.append(issue.field)
                elif (issue.severity == ValidationSeverity.WARNING and
                      "not provided" in issue.message.lower()):
                    missing_fields.append(issue.field)

            return list(set(missing_fields))  # Remove duplicates

        except Exception as e:
            self.logger.error(f"Missing fields extraction failed: {str(e)}")
            return []

    def _rule_based_transform_profile(self, linkedin_profile: LinkedInProfile) -> TransformedCandidate:
        """Fallback rule-based transformation when LLM fails."""
        try:
            # Use the comprehensive schema mapper
            candidate_info = map_linkedin_profile_to_smarthr(linkedin_profile)

            # Calculate confidence based on data completeness
            confidence = self._calculate_rule_based_confidence(candidate_info)

            # Identify missing fields
            missing_fields = [
                field for field in self.required_fields
                if candidate_info.get(field) == "not_provided" or not candidate_info.get(field)
            ]

            return TransformedCandidate(
                candidate_info=candidate_info,
                source_linkedin_profile=linkedin_profile,
                transformation_confidence=confidence,
                transformation_notes="Rule-based transformation using comprehensive schema mapper",
                missing_fields=missing_fields,
                additional_fields=[]
            )

        except Exception as e:
            self.logger.error(f"Rule-based transformation failed: {str(e)}")
            return self._minimal_transform_profile(linkedin_profile)

    def _calculate_rule_based_confidence(self, candidate_info: Dict[str, Any]) -> float:
        """Calculate confidence score for rule-based transformation."""
        try:
            total_fields = len(self.required_fields)
            provided_fields = 0

            for field in self.required_fields:
                value = candidate_info.get(field)
                if value and value != "not_provided":
                    if isinstance(value, list) and len(value) > 0:
                        provided_fields += 1
                    elif isinstance(value, str) and len(value.strip()) > 0:
                        provided_fields += 1
                    elif not isinstance(value, (list, str)):
                        provided_fields += 1

            base_confidence = provided_fields / total_fields

            # Bonus for rich data
            if candidate_info.get("experience") and len(candidate_info["experience"]) > 0:
                base_confidence += 0.1

            if candidate_info.get("skills") and len(candidate_info["skills"]) > 3:
                base_confidence += 0.1

            if candidate_info.get("education") and len(candidate_info["education"]) > 0:
                base_confidence += 0.05

            return min(base_confidence, 1.0)

        except Exception as e:
            self.logger.error(f"Confidence calculation failed: {str(e)}")
            return 0.5

    def _minimal_transform_profile(self, linkedin_profile: LinkedInProfile) -> TransformedCandidate:
        """Minimal transformation as last resort."""
        candidate_info = {
            "first_name": linkedin_profile.first_name or "Unknown",
            "last_name": linkedin_profile.last_name or "Unknown",
            "email": "not_provided",
            "phone": "not_provided",
            "location": "not_provided",
            "current_position": linkedin_profile.headline or "not_provided",
            "professional_summary": "not_provided",
            "experience": [],
            "education": [],
            "skills": []
        }

        return TransformedCandidate(
            candidate_info=candidate_info,
            source_linkedin_profile=linkedin_profile,
            transformation_confidence=0.3,  # Very low confidence
            transformation_notes="Minimal transformation due to processing errors",
            missing_fields=["email", "phone", "location", "professional_summary"],
            additional_fields=[]
        )


# Agent factory functions
def create_linkedin_search_agent(config: Optional[LinkedInIntegrationConfig] = None) -> LinkedInSearchAgent:
    """Factory function to create LinkedIn Search Agent."""
    return LinkedInSearchAgent(config)


def create_schema_transformation_agent(config: Optional[LinkedInIntegrationConfig] = None) -> SchemaTransformationAgent:
    """Factory function to create Schema Transformation Agent."""
    return SchemaTransformationAgent(config)
