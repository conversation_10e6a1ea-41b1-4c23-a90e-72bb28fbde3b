#!/bin/bash

# LinkedIn Integration API - cURL Test Commands
# Tests the updated JSON structure with only database-accessible fields

BASE_URL="http://localhost:8080/api/external_source"

echo "🚀 LinkedIn Integration API - cURL Tests"
echo "========================================"
echo "Testing updated JSON structure (database-accessible fields only)"
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print test headers
print_test_header() {
    echo -e "\n${BLUE}📋 $1${NC}"
    echo "----------------------------------------"
}

# Function to check if server is running
check_server() {
    print_test_header "Checking Server Status"
    
    if curl -s -f "$BASE_URL/status" > /dev/null; then
        echo -e "${GREEN}✅ Server is running${NC}"
        return 0
    else
        echo -e "${RED}❌ Server is not running or not responding${NC}"
        echo "Please make sure the Docker containers are running:"
        echo "  docker-compose up"
        return 1
    fi
}

# Test 1: Basic Search
test_basic_search() {
    print_test_header "Basic Search Test"
    
    curl -X POST "$BASE_URL/search" \
        -H "Content-Type: application/json" \
        -H "Accept: application/json" \
        -d '{
            "keywords": ["software engineer"],
            "location": "San Francisco, CA",
            "limit": 5,
            "transform_profiles": true,
            "include_raw_profiles": false
        }' \
        -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n" \
        -s | jq '.' 2>/dev/null || echo "Response received (jq not available for formatting)"
}

# Test 2: Detailed Search with All Fields
test_detailed_search() {
    print_test_header "Detailed Search Test (All Available Fields)"
    
    curl -X POST "$BASE_URL/search" \
        -H "Content-Type: application/json" \
        -H "Accept: application/json" \
        -d '{
            "keywords": [
                "senior software engineer",
                "python developer",
                "backend engineer"
            ],
            "location": "San Francisco Bay Area",
            "experience_level": "senior",
            "skills": [
                "Python",
                "Django",
                "FastAPI",
                "PostgreSQL",
                "Docker",
                "AWS"
            ],
            "school": "Stanford University",
            "first_name": "John",
            "last_name": "Smith",
            "country": "United States",
            "city": "San Francisco",
            "degree": "Computer Science",
            "years_of_experience": "5-10",
            "languages": ["English", "Spanish"],
            "past_title": "Software Developer",
            "limit": 10,
            "transform_profiles": true,
            "include_raw_profiles": false
        }' \
        -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n" \
        -s | jq '.' 2>/dev/null || echo "Response received (jq not available for formatting)"
}

# Test 3: Batch Search
test_batch_search() {
    print_test_header "Batch Search Test"
    
    curl -X POST "$BASE_URL/search/batch" \
        -H "Content-Type: application/json" \
        -H "Accept: application/json" \
        -d '{
            "search_requests": [
                {
                    "keywords": ["data scientist"],
                    "location": "New York, NY",
                    "skills": ["Python", "TensorFlow", "SQL"],
                    "limit": 3
                },
                {
                    "keywords": ["frontend developer"],
                    "location": "Austin, TX",
                    "skills": ["React", "JavaScript"],
                    "limit": 3
                }
            ],
            "transform_profiles": true
        }' \
        -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n" \
        -s | jq '.' 2>/dev/null || echo "Response received (jq not available for formatting)"
}

# Test 4: Configuration Check
test_configuration() {
    print_test_header "Configuration Test"
    
    echo "Checking API configuration..."
    curl -X GET "$BASE_URL/config" \
        -H "Accept: application/json" \
        -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n" \
        -s | jq '.' 2>/dev/null || echo "Response received (jq not available for formatting)"
}

# Test 5: Status Check
test_status() {
    print_test_header "Status Test"
    
    echo "Checking system status..."
    curl -X GET "$BASE_URL/status" \
        -H "Accept: application/json" \
        -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n" \
        -s | jq '.' 2>/dev/null || echo "Response received (jq not available for formatting)"
}

# Test 6: Test with Removed Fields (Should Fail or Ignore)
test_removed_fields() {
    print_test_header "Removed Fields Test (Should Handle Gracefully)"
    
    echo -e "${YELLOW}⚠️  Testing with removed fields (current_title, current_company, past_company)${NC}"
    echo "These fields should be ignored or cause validation errors:"
    
    curl -X POST "$BASE_URL/search" \
        -H "Content-Type: application/json" \
        -H "Accept: application/json" \
        -d '{
            "keywords": ["software engineer"],
            "location": "San Francisco, CA",
            "current_title": "Senior Engineer",
            "current_company": "Tech Corp",
            "past_company": "Old Corp",
            "limit": 5,
            "transform_profiles": true
        }' \
        -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n" \
        -s | jq '.' 2>/dev/null || echo "Response received (jq not available for formatting)"
}

# Test 7: Health Check
test_health() {
    print_test_header "Health Check Test"
    
    echo "Checking application health..."
    curl -X GET "$BASE_URL/health" \
        -H "Accept: application/json" \
        -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n" \
        -s 2>/dev/null || echo "Health endpoint may not be available"
}

# Main execution
main() {
    echo -e "${BLUE}Starting LinkedIn Integration API Tests...${NC}"
    
    # Check if server is running first
    if ! check_server; then
        exit 1
    fi
    
    # Run all tests
    test_configuration
    test_status
    test_basic_search
    test_detailed_search
    test_batch_search
    test_removed_fields
    test_health
    
    echo -e "\n${GREEN}🎉 All tests completed!${NC}"
    echo -e "${YELLOW}💡 Tips:${NC}"
    echo "  - Install 'jq' for better JSON formatting: apt-get install jq"
    echo "  - Check Docker logs if tests fail: docker-compose logs smarthr-backend"
    echo "  - Use the Python test script for more detailed validation"
}

# Help function
show_help() {
    echo "LinkedIn Integration API - cURL Test Script"
    echo ""
    echo "Usage: $0 [OPTION]"
    echo ""
    echo "Options:"
    echo "  help              Show this help message"
    echo "  check             Check if server is running"
    echo "  basic             Run basic search test only"
    echo "  detailed          Run detailed search test only"
    echo "  batch             Run batch search test only"
    echo "  config            Check configuration only"
    echo "  status            Check status only"
    echo "  removed           Test removed fields handling"
    echo "  all               Run all tests (default)"
    echo ""
    echo "Examples:"
    echo "  $0                # Run all tests"
    echo "  $0 basic          # Run basic search test only"
    echo "  $0 check          # Check if server is running"
}

# Handle command line arguments
case "${1:-all}" in
    "help"|"-h"|"--help")
        show_help
        ;;
    "check")
        check_server
        ;;
    "basic")
        check_server && test_basic_search
        ;;
    "detailed")
        check_server && test_detailed_search
        ;;
    "batch")
        check_server && test_batch_search
        ;;
    "config")
        check_server && test_configuration
        ;;
    "status")
        check_server && test_status
        ;;
    "removed")
        check_server && test_removed_fields
        ;;
    "all"|*)
        main
        ;;
esac
