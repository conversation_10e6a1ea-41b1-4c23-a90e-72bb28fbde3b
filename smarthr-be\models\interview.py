from datetime import datetime
from pydantic import BaseModel, Field
from typing import List, Optional
from enum import Enum


# ─────────────────────────────── core enums ───────────────────────────────────
class ProcessType(str, Enum):
    EXTRACT = "extract"
    PARAPHRASE = "paraphrase"


class Seniority(str, Enum):
    SENIOR = "senior"
    MID = "mid"
    JUNIOR = "junior"
    NA = "n/a"


class InterviewStatus(str, Enum):
    PENDING = "pending"
    SCHEDULED = "scheduled"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


# ─────────────────────────── request/response models ──────────────────────────
class InterviewProcessingRequest(BaseModel):
    questions: List[str]
    transcript: str
    process_type: ProcessType


class ExtractedAnswers(BaseModel):
    answers: List[str]


class ParaphrasedAnswerDetail(BaseModel):
    answer: str
    paraphrased: str
    complement_from: Optional[str] = None
    # complement_from: Optional[List[str]] = []


class ParaphrasedAnswers(BaseModel):
    answers: List[ParaphrasedAnswerDetail]


# ───────────────────────── questionnaire models ───────────────────────────────
class QuestionAnswer(BaseModel):
    question_number: int = Field(..., description="Question number for tracking")
    question: str
    senior_answer: str
    mid_answer: str
    junior_answer: str
    tag: str = Field(..., description="Tag to uniquely categorize the question with only one of the following: 'Technical Skills', 'Soft Skills', 'Methodologies', 'Language - Tools'")


class QA_model(BaseModel):
    questions: List[QuestionAnswer]


# ───────────────────── evaluation (scoring) models ────────────────────────────
class QuestionEvaluation(BaseModel):
    question_number: int
    expected_seniority: Seniority
    detected_seniority: Seniority
    explanation: str = Field(..., description="Explanation of the evaluation, including reasoning for the detected seniority level.")


class EvaluateInterviewNoQA(BaseModel):
    overall_seniority: Seniority
    percentage_of_match: float
    explanation: str = Field(..., description="Explanation of the evaluation, including reasoning for the overall seniority and percentage of match.")


class TranscriptQuestion(BaseModel):
    question_number: int
    question_text: str
    category: Optional[str] = None  # e.g., "Technical Skills", "Soft Skills"

class TranscriptQuestions(BaseModel):
    questions: List[TranscriptQuestion]

class EvaluationResult(BaseModel):
    overall_seniority: Seniority
    per_question: List[QuestionEvaluation]
    percentage_of_match: float
    explanation: str = Field(..., description="Overall explanation of the evaluation, including reasoning for the overall seniority and percentage of match. Include the feedback tec comments in the last paragraph of the evaluation.")


# ─────────────────────── 4-Agent Evaluation System Models ──────────────────────
class TranscriptQAPair(BaseModel):
    question_number: int
    question_text: str
    answer_text: str
    has_valid_response: bool = Field(..., description="Whether the candidate provided a meaningful response (not just 'I don't know' or nonsensical answers)")
    response_quality: str = Field(..., description="Quality assessment: 'valid', 'inadequate', 'invalid', or 'missing'")


class ParsedTranscript(BaseModel):
    qa_pairs: List[TranscriptQAPair]
    total_questions_found: int
    parsing_notes: str = Field(..., description="Notes about the parsing process and any issues encountered")


class ExpectedResponseSet(BaseModel):
    question_number: int
    question_text: str
    junior_answer: str
    mid_answer: str
    senior_answer: str
    question_category: str = Field(default="Unknown", description="Category like 'Technical Skills', 'Soft Skills', etc.")


class ExpectedResponses(BaseModel):
    responses: List[ExpectedResponseSet]
    matching_notes: str = Field(..., description="Notes about how transcript questions were matched to database questions")


class IndividualQuestionEvaluation(BaseModel):
    question_number: int
    question_text: str
    actual_answer: str
    expected_answers: ExpectedResponseSet
    is_valid_response: bool = Field(..., description="Whether the response is valid (not completely wrong or nonsensical)")
    is_dont_know_response: bool = Field(default=False, description="Whether the response is an 'I don't know' type answer")
    detected_seniority: Seniority
    confidence_score: float = Field(..., description="Confidence in the seniority detection (0.0 to 1.0)")
    evaluation_reasoning: str = Field(..., description="Detailed reasoning for the evaluation")
    similarity_to_junior: float = Field(default=0.0, description="Similarity score to junior answer (0.0 to 1.0)")
    similarity_to_mid: float = Field(default=0.0, description="Similarity score to mid answer (0.0 to 1.0)")
    similarity_to_senior: float = Field(default=0.0, description="Similarity score to senior answer (0.0 to 1.0)")


class FourAgentEvaluationResult(BaseModel):
    overall_seniority: Seniority
    individual_evaluations: List[IndividualQuestionEvaluation]
    total_questions: int
    valid_responses: int
    invalid_responses: int
    percentage_of_correct_answers: float = Field(..., description="Percentage based only on valid responses")
    seniority_distribution: dict = Field(..., description="Count of responses at each seniority level")
    evaluation_summary: str = Field(..., description="Comprehensive summary of the evaluation")
    agent_processing_notes: str = Field(..., description="Notes from each agent's processing")


# ─────────────────────── Agent Request/Response Models ──────────────────────
class Agent1Request(BaseModel):
    transcript: str
    interview_id: str


class Agent1Response(BaseModel):
    parsed_transcript: ParsedTranscript
    success: bool
    error_message: Optional[str] = None


class Agent2Request(BaseModel):
    parsed_transcript: ParsedTranscript
    position_id: str
    interview_id: str


class Agent2Response(BaseModel):
    expected_responses: ExpectedResponses
    success: bool
    error_message: Optional[str] = None


class Agent3Request(BaseModel):
    qa_pair: TranscriptQAPair
    expected_response: ExpectedResponseSet
    interview_id: str


class Agent3Response(BaseModel):
    evaluation: IndividualQuestionEvaluation
    success: bool
    error_message: Optional[str] = None


class Agent4Request(BaseModel):
    individual_evaluations: List[IndividualQuestionEvaluation]
    parsed_transcript: ParsedTranscript
    interview_id: str
    position_info: Optional[dict] = None
    candidate_info: Optional[dict] = None


class Agent4Response(BaseModel):
    final_evaluation: FourAgentEvaluationResult
    success: bool
    error_message: Optional[str] = None


class FourAgentEvaluationRequest(BaseModel):
    interview_id: str
    force_reevaluation: bool = False


# ─────────────────────────────── interview models ──────────────────────────────
class InterviewBase(BaseModel):
    id: str
    position_id: str
    candidate_id: str
    candidate_info: Optional[dict] = None
    position_info: Optional[dict] = None
    interview_data: Optional[dict] = None
    anwers_data: Optional[dict] = None
    created_at: datetime
    updated_at: datetime


class Interview(InterviewBase):
    feedback_hr: Optional[dict] = None
    recruiter_hr_id: Optional[str] = None
    scheduled_hr_id: Optional[str] = None
    interview_date_hr: Optional[datetime] = None
    feedback_date_hr: Optional[datetime] = None
    status_hr: Optional[str] = None
    recommendation_hr: Optional[bool] = None
    transcript_hr: Optional[str] = None
    feedback_tec: Optional[dict] = None  
    recruiter_tec_id: Optional[str] = None
    scheduled_tec_id: Optional[str] = None
    interview_date_tec: Optional[datetime] = None
    feedback_date_tec: Optional[datetime] = None
    status_tec: Optional[str] = None
    recommendation_tec: Optional[bool] = None
    transcript_tec: Optional[str] = None,
    analysis_data: Optional[dict] = None  # Analysis data for the interview


class InterviewHr(BaseModel):
    position_id: str
    candidate_id: str 
    recruiter_hr_id: str
    scheduled_hr_id: str  
    feedback_hr: dict
    interview_date_hr: datetime
    feedback_date_hr: datetime
    status_hr: str
    recommendation_hr: bool
    transcript_hr: str


class InterviewTec(BaseModel):
    position_id: str
    candidate_id: str  
    recruiter_tec_id: str
    scheduled_tec_id: str 
    feedback_tec: dict
    interview_date_tec: datetime
    feedback_date_tec: datetime
    status_tec: str
    recommendation_tec: bool
    transcript_tec: str


class InterviewCreate(BaseModel):
    candidate_id: str
    analysis_data: dict  # Analysis data for the interview
