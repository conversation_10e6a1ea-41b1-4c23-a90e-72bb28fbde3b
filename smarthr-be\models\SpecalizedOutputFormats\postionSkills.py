from pydantic import BaseModel, Field
from typing import List, Optional
from enum import Enum


class SkillImportance(str, Enum):
    """Enum for skill importance levels in matching"""
    CRITICAL = "critical"  # Must-have skills for the role
    IMPORTANT = "important"  # Highly valuable skills
    NICE_TO_HAVE = "nice_to_have"  # Beneficial but not essential


class SkillCategory(str, Enum):
    """Enum for skill categories to help with matching logic"""
    CORE_COMPETENCY = "core_competency"  # Primary job function skills (e.g., QA for QA roles)
    TECHNICAL_TOOL = "technical_tool"  # Specific technologies/tools (e.g., Salesforce, Python)
    METHODOLOGY = "methodology"  # Processes and approaches (e.g., Agile, DevOps)
    SOFT_SKILL = "soft_skill"  # Communication, leadership, etc.
    DOMAIN_KNOWLEDGE = "domain_knowledge"  # Industry or business domain expertise


class ExplicitSkill(BaseModel):
    name: str = Field(..., description="One of the most important skills described in the skills section.")
    priority: int = Field(0, description="Number from 0 to 10 of importance")  # Default value

class InferencedSkill(BaseModel):
    name: str = Field(..., description="One of the most important skills inferred from the description.")
    priority: int = Field(0, description="Number from 0 to 10 of importance rebalanced by your consideration.")


class PrioritizedSkill(BaseModel):
    """Enhanced skill model for matching purposes with importance and category"""
    name: str = Field(..., description="Name of the skill (e.g., 'Quality Assurance', 'Salesforce Administration')")
    importance: SkillImportance = Field(..., description="Importance level of this skill for the specific position")
    category: SkillCategory = Field(..., description="Category of the skill to help with matching logic")
    weight: float = Field(..., description="Numerical weight for matching calculations (0.0 to 1.0)", ge=0.0, le=1.0)
    description: Optional[str] = Field(None, description="Brief explanation of why this skill is important for this role")


class PositionSkillAnalysis(BaseModel):
    """Enhanced skill analysis model specifically designed for candidate matching"""
    position_title: str = Field(..., description="The job title or position name")
    primary_role_function: str = Field(..., description="The core job function (e.g., 'Quality Assurance', 'Software Development')")
    prioritized_skills: List[PrioritizedSkill] = Field(..., description="List of skills prioritized by importance for matching")
    skill_weight_rationale: str = Field(..., description="Explanation of how skills were prioritized for this specific position")


class TopSkills(BaseModel):
    skills_from_explicit: List[ExplicitSkill] = Field(None, description="List of the top skills described explicitly.")
    skills_from_description: List[InferencedSkill] = Field(None, description="List of the top skills not setted as skill but infered from the description.")