CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- Enable pgvector for vector operations
CREATE EXTENSION IF NOT EXISTS vector;

-- Enable fuzzystrmatch extension for fuzzy string matching
CREATE EXTENSION IF NOT EXISTS fuzzystrmatch;

-- Create or update the projects table
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'projects') THEN
        CREATE TABLE projects (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            client_name TEXT,
            name TEXT,
            description TEXT,
            status BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT NOW(),
            updated_at TIMESTAMP DEFAULT NOW()
        );
    END IF;
END $$;

-- Create or update the candidates_smarthr table
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'candidates_smarthr') THEN
        CREATE TABLE candidates_smarthr (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            proj_id UUID REFERENCES projects(id),
            candidate_info JSONB,
            suggested_positions JSONB,
            analysis_status TEXT,
            to_be_embebbed TEXT,
            embedding VECTOR(1536),
            sparse_embedding JSONB,
            Reason_Info JSONB,
            is_active BOOLEAN DEFAULT true,
            is_deleted BOOLEAN DEFAULT false,
            last_matching TIMESTAMP,
            created_at TIMESTAMP DEFAULT NOW(),
            created_by TEXT,
            updated_at TIMESTAMP DEFAULT NOW(),
            updated_by TEXT
        );
    END IF;
END $$;

-- Create or update the positions_smarthr table
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'positions_smarthr') THEN
        CREATE TABLE positions_smarthr (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            proj_id UUID REFERENCES projects(id),
            position_info JSONB,
            top_candidates JSONB,
            to_be_embebbed TEXT,
            embedding VECTOR(1536),
            sparse_embedding JSONB,
            last_matching TIMESTAMP,
            created_at TIMESTAMP DEFAULT NOW(),
            updated_at TIMESTAMP DEFAULT NOW(),
            external_id TEXT
        );
    END IF;
END $$;

-- Create an index on the embedding column of candidates_smarthr for HNSW-based vector operations
CREATE INDEX IF NOT EXISTS candidates_embedding_idx 
ON candidates_smarthr USING hnsw (embedding vector_cosine_ops);

/* ─────────────────────────────────────────────────────────────────────────────
   ENUM  interview_status   (avoid magic integers)
───────────────────────────────────────────────────────────────────────────── */
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'interview_status') THEN
        CREATE TYPE interview_status AS ENUM
            ('scheduled', 'in_progress', 'completed', 'cancelled', 'not_scheduled');
    END IF;
END $$;

/* ─────────────────────────────────────────────────────────────────────────────
   TABLE  interview_questions   (1‑to‑1 with position)
───────────────────────────────────────────────────────────────────────────── */
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.tables
        WHERE table_name = 'interview_questions'
    ) THEN
        CREATE TABLE interview_questions (
            id           UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            position_id  UUID NOT NULL
                         REFERENCES positions_smarthr(id) ON DELETE CASCADE,
            data         JSONB,         -- 20 Q&A in QA_model format
            created_at   TIMESTAMP DEFAULT NOW(),
            created_by   TEXT,
            updated_at   TIMESTAMP DEFAULT NOW(),
            updated_by   TEXT,
            allow_regeneration   BOOLEAN DEFAULT TRUE, -- new column to saved question flag and do not allow regeneration
            UNIQUE (position_id)        -- only one questionnaire per position
        );
    END IF;
END $$;

/* ─────────────────────────────────────────────────────────────────────────────
   TABLE  interviews
───────────────────────────────────────────────────────────────────────────── */
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.tables
        WHERE table_name = 'interviews'
    ) THEN
        CREATE TABLE interviews (
            id                UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            -- Foreign keys
            position_id       UUID NOT NULL REFERENCES positions_smarthr(id) ON DELETE CASCADE,
            candidate_id      UUID NOT NULL REFERENCES candidates_smarthr(id) ON DELETE CASCADE,
            analysis_data    JSONB,  -- Analysis data for the interview
            -- HR phase
            feedback_hr        JSONB,
            recruiter_hr_id    TEXT,
            scheduled_hr_id    TEXT,
            interview_date_hr  TIMESTAMP,
            feedback_date_hr   TIMESTAMP,
            status_hr          interview_status,
            recommendation_hr  BOOLEAN,
            transcript_hr      TEXT,
            -- Technical phase
            feedback_tec       JSONB,
            recruiter_tec_id   TEXT,
            scheduled_tec_id   TEXT,
            interview_date_tec TIMESTAMP,
            feedback_date_tec  TIMESTAMP,
            status_tec         interview_status,
            recommendation_tec BOOLEAN,
            transcript_tec     TEXT,
            -- LLM outputs
            anwers_data   JSONB,
            interview_data JSONB,
            created_at TIMESTAMP DEFAULT NOW(),
            updated_at TIMESTAMP DEFAULT NOW()
        );
    END IF;
END $$;

/* ─────────────────────────────────────────────────────────────────────────────
   TABLE candidate_notes (1‑to‑* with candidates_smarthr)
───────────────────────────────────────────────────────────────────────────── */
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.tables
        WHERE table_name = 'candidate_notes'
    ) THEN
        CREATE TABLE candidate_notes (
            id              UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            candidate_id    UUID NOT NULL REFERENCES candidates_smarthr(id) ON DELETE CASCADE,
            notes           JSONB,  -- notes in JSON format
            created_by      TEXT,   -- user's email who created the note
            created_at      TIMESTAMP DEFAULT NOW(),
            updated_by      TEXT,   -- user's email who last updated the note
            updated_at      TIMESTAMP DEFAULT NOW()
        );
    END IF;
END $$;

/* ─────────────────────────────────────────────────────────────────────────────
   TABLE  professionals
───────────────────────────────────────────────────────────────────────────── */
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.tables
        WHERE table_name = 'professionals'
    ) THEN
        CREATE TABLE IF NOT EXISTS professionals (
        id                  UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        professional_info   JSONB,
        source          TEXT,
        reason          TEXT,
        to_be_embebbed  TEXT,
        embedding       VECTOR(1536),
        is_active       BOOLEAN DEFAULT true,
        is_deleted      BOOLEAN DEFAULT false,
        created_at      TIMESTAMP DEFAULT NOW(),
        created_by      TEXT,
        updated_at      TIMESTAMP DEFAULT NOW(),
        updated_by      TEXT
    );
    END IF;
END $$;

/* ─────────────────────────────────────────────────────────────────────────────
   TRIGGER  updated_at
───────────────────────────────────────────────────────────────────────────── */
CREATE OR REPLACE FUNCTION trg_set_updated_at()
RETURNS TRIGGER LANGUAGE plpgsql AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$;

DROP TRIGGER IF EXISTS set_updated_at_interviews ON interviews;
CREATE TRIGGER set_updated_at_interviews
BEFORE UPDATE ON interviews
FOR EACH ROW EXECUTE FUNCTION trg_set_updated_at();



/* ─────────────────────────────────────────────────────────────────────────────
   INDEXES
───────────────────────────────────────────────────────────────────────────── */
CREATE INDEX IF NOT EXISTS idx_interviews_position   ON interviews(position_id);
CREATE INDEX IF NOT EXISTS idx_interviews_candidate  ON interviews(candidate_id);
CREATE INDEX IF NOT EXISTS idx_interviews_status_hr  ON interviews(status_hr);
CREATE INDEX IF NOT EXISTS idx_interviews_status_tec ON interviews(status_tec);